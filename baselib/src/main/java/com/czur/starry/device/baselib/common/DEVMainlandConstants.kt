package com.czur.starry.device.baselib.common

/**
 * Created by 陈丰尧 on 2022/1/24
 * 测试-国内环境常量
 */

internal object DEVMainlandConstants : EnvConstants {
    override val BASE_HOST = "test0927-starry.czur.cc"
    override val BASE_URL_SHARE_V2 = "https://starry-share-v2-test.czur.cc"
    override val PASSPORT_COUNTRY_URL: String = "https://test-passport.czur.cc"
    override val PERSONAL_WEB_URL = "https://starry-hybrid0927.czur.cc/#/"
    override val OTA_BASE_URL = "https://starry-ota-test.czur.com"
    override val APP_STORE_BASE_URL = "https://starry-market0927-v2.czur.cc"
    override val TMP_BUCKET_NAME = "czur-public"
    override val DOWNLOAD_HOST = "https://czur-public.oss-cn-beijing.aliyuncs.com"
    override val TMP_END_POINT = "https://oss-cn-beijing.aliyuncs.com"
    //修改为动态获取，目前废弃
    override val WELCOME_BASE_URL = "https://starry-share-v2-test.czur.cc/welcomeImage?sn="
    override val CHECK_OUT_HOST_URL = "https://checkout-test.czur.cc"

    override val MEETING_APP_ID = "31def79085e74ad689b71dd7a9f12174"

    // OTA
    override val OTA_APP_ID = "1b182ac6eab24dbe8a8d40f04a182082"
    override val OTA_CLIENT_ID = "CZUR-ADMIN-TEST"
    override val OTA_WEB_CLIENT_ID = "CZUR-WEB-ADMIN-TEST"
    override val OTA_ONLINE_CHANNEL = "CZUR-ONLINE-TEST"
    override val OTA_UPDATE_INFO: Int = 1

    override val BASE_NOTES_HOST: String = "https://starry-hybridnotes0927.czur.cc/#"

    override val NETTY_SERVER_IP: String = "**************"
    override val MEETING_SHARE_HOST: String = "https://meeting-test.czur.cc"
    override val OFFIC_PREVIEW_URL: String = "http://ow365.cn/?i=24342&ssl=1&furl="
    override val FEED_BACK_EMAIL: String = "<EMAIL>"

    override val VIDEO_ID_WINDOWS = 3
    override val VIDEO_ID_ANDROID = 1
    override val VIDEO_ID_APPLE = 2
    override val VIDEO_ID_LINUX = 13

    override val VIDEO_ID_DLNA = 9
    override val VIDEO_ID_SHARE = 10
    override val VIDEO_ID_CLICK_DROP = 4
    override val VIDEO_ID_P2P = 11
}
