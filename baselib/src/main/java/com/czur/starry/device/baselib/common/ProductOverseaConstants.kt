package com.czur.starry.device.baselib.common

/**
 * Created by 陈丰尧 on 2022/1/24
 * 生产-海外环境境常量
 */
internal object ProductOverseaConstants : EnvConstants {
    override val BASE_HOST = "starry-global.czur.cc"
    override val PASSPORT_COUNTRY_URL: String = "https://passport.czur.cc"
    override val BASE_URL_SHARE_V2 = "https://starry-share-v2-na.czur.cc"
    override val PERSONAL_WEB_URL = "https://starry-hybrid-api4-na.czur.cc/#/"
    override val OTA_BASE_URL = "https://starry-ota.czur.com"
    override val APP_STORE_BASE_URL = "https://starry-market.czur.cc"
    override val TMP_BUCKET_NAME = "czur-public-na"
    override val DOWNLOAD_HOST = "https://czur-public-na.oss-us-west-1.aliyuncs.com"
    override val TMP_END_POINT = "https://oss-us-west-1.aliyuncs.com"
    override val WELCOME_BASE_URL = "https://starry-share.czur.cc/#/welcomeImage?sn="
    override val CHECK_OUT_HOST_URL = "https://checkout.czur.cc"


    override val MEETING_APP_ID = "3415448312c84e1189b37e2611fef2af"

    // OTA
    override val OTA_APP_ID = "1b182ac6eab24dbe8a8d40f04a182082"
    override val OTA_CLIENT_ID = "CZUR-ADMIN-PRD"
    override val OTA_WEB_CLIENT_ID = "CZUR-WEB-ADMIN-PRD"
    override val OTA_ONLINE_CHANNEL = "CZUR-ONLINE-PRD"
    override val BASE_NOTES_HOST: String = "https://starry-hybridnotes-na.czur.cc/#"
    override val OTA_UPDATE_INFO: Int = 0

    override val NETTY_SERVER_IP: String = "************"
    override val MEETING_SHARE_HOST: String = "https://meeting.czur.cc"
    override val OFFIC_PREVIEW_URL: String = "http://ow365.cn/?i=29806&ssl=1&furl="
    override val FEED_BACK_EMAIL: String = "<EMAIL>"

    override val VIDEO_ID_WINDOWS = 15
    override val VIDEO_ID_ANDROID = 9
    override val VIDEO_ID_APPLE = 12
    override val VIDEO_ID_LINUX = 13

    override val VIDEO_ID_DLNA = 11
    override val VIDEO_ID_SHARE = 14
    override val VIDEO_ID_CLICK_DROP = 10
    override val VIDEO_ID_P2P = 11
}
