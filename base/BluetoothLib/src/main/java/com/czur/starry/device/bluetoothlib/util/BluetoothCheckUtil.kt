package com.czur.starry.device.bluetoothlib.util

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/7/24
 */
private const val LINUX_CHECK_CMD = "cat /proc/bus/input/devices"
private const val LINUX_CHECK_MAC_PREFIX = "U: "

/**
 * 获取蓝牙连接的鼠标mac地址列表
 */
suspend fun getInputBTConnectMacList(): List<String> = withContext(Dispatchers.IO) {
    val process = Runtime.getRuntime().exec(LINUX_CHECK_CMD)
    process.inputStream.bufferedReader().useLines { lines ->
        lines.filter { it.startsWith(LINUX_CHECK_MAC_PREFIX) }
            .map { it.substringAfter("=", "").uppercase() }
            .filter { it.length == 17 } // mac地址长度
            .map {
                // 翻转mac地址, 获取到的mac是蓝牙mac地址的倒序
                it.split(":").reduceRight { text, acc ->
                    "${acc}:${text}"
                }
            }
            .toList()
    }

}