package com.czur.starry.device.settings.ui.projector.bluetooth

import android.annotation.SuppressLint
import android.app.Application
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothProfile
import android.media.AudioDeviceCallback
import android.media.AudioDeviceInfo
import android.media.AudioManager
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.bluetoothlib.bluetooth.BluetoothCallback
import com.czur.starry.device.bluetoothlib.bluetooth.CachedBluetoothDevice
import com.czur.starry.device.bluetoothlib.bluetooth.LocalBluetoothManager
import com.czur.starry.device.bluetoothlib.util.getInputBTConnectMacList
import com.czur.starry.device.bluetoothlib.util.isHeadPhones
import com.czur.starry.device.bluetoothlib.util.isSpeaker
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.BTBoundDeviceEntity
import com.czur.starry.device.settings.touchpad.isTouchPad
import com.czur.starry.device.settings.touchpad.isWritePad
import com.czur.starry.device.settings.ui.projector.bluetooth.net.IWhiteListService
import com.czur.starry.device.settings.utils.isBluetoothDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2023/7/11
 */
private const val TAG = "BTDeviceViewModel"

class BTDeviceViewModel(application: Application) : AndroidViewModel(application) {
    val whiteListService: IWhiteListService by lazy { HttpManager.getService(Constants.OTA_BASE_URL) }
    var whiteList: List<WhiteListItem> = emptyList()

    private val dockBluetoothCallback = DockBluetoothCallback()


    private val localManager: LocalBluetoothManager =
        LocalBluetoothManager.getInstance(application, null)
            .also { it!!.eventManager.registerCallback(dockBluetoothCallback) }!!

    private val localAdapter by lazy { localManager.bluetoothAdapter }

    val isScanningFlow = MutableStateFlow(localAdapter.isDiscovering)
    val isScanning: Boolean
        get() = isScanningFlow.value

    // 可用设备
    private val allDevicesFlow = MutableStateFlow(CachedDevicesWrapper(emptyList()))
    val availableDevicesFlow = allDevicesFlow.map {
        it.cachedDevices.filter { device ->
            (BTDeviceFilter.showBTDevices(
                whiteList,
                device.device
            ) && device.bondState != BluetoothDevice.BOND_BONDED)
        }
    }.flowOn(Dispatchers.IO)

    // 通过Linux的命令来查看真正连接的蓝牙设备mac地址
    private val connectInputBTMacFlow = flow {
        while (true) {
            emit(getInputBTConnectMacList())
            delay(ONE_SECOND * 2)
        }
    }

    // 蓝牙音频设备的mac地址
    private val connectAudioBTMacFlow = MutableStateFlow<List<String>>(emptyList())
    private val connectBTMacFlow = combine(connectInputBTMacFlow, connectAudioBTMacFlow) { inputMacs, audioMacs ->
        (inputMacs + audioMacs).distinct().map { it.uppercase() }
    }.flowOn(Dispatchers.Default)

    // 已配对设备
    private val boundDevicesFlow = allDevicesFlow.map {
        val cachedDeviceList = it.cachedDevices
        cachedDeviceList.filter { device ->
            device.bondState == BluetoothDevice.BOND_BONDED
                    && !device.device.isWritePad()  // 不显示写字板
                    && !device.device.isTouchPad()  // 不显示触控板
        }
    }
        .flowOn(Dispatchers.IO)
        .onStart {
            localManager.bluetoothAdapter.bondedDevices.mapNotNull {
                val findDevice: CachedBluetoothDevice? =
                    localManager.cachedDeviceManager.findDevice(it)
                findDevice ?: localManager.cachedDeviceManager.addDevice(
                    it
                )
            }
        }.combine(connectBTMacFlow.distinctUntilChanged()) { boundDevices, connMacList ->
            boundDevices.map {
                val isConnect = connMacList.contains(it.device.address.uppercase())
                if (it.isConnected != isConnect) {
                    logTagW(
                        TAG,
                        " isConnectByLinux: $isConnect , 以Linux为准"
                    )
                }
                it
            }
        }.map {
            // 根据mac地址去重, 有时候会出现两个一样的设备
            it.distinctBy { device ->
                device.device.address.uppercase()
            }
        }
        .flowOn(Dispatchers.IO)
    private val timerRefreshFlow = flow {
        while (true) {
            emit(Unit)
            delay(ONE_MIN)
        }
    }

    // 用来手动刷新的Flow
    private val manualRefreshFlow = MutableStateFlow(System.currentTimeMillis())

    private val refreshFlow = manualRefreshFlow.combine(timerRefreshFlow) { _, _ ->
        System.currentTimeMillis()
    }.flowOn(Dispatchers.IO)

    // 正在忽略的设备mac地址
    private val ignoringMacFlow = MutableStateFlow<Set<String>>(emptySet())

    // 我的设备
    val myDeviceFlow =
        boundDevicesFlow.combine(refreshFlow) { boundDevices, _ ->
            val touchBoardProBatteryMap = getTouchBoardProBatteryLevel()
            updateMyDeviceInfo(boundDevices, touchBoardProBatteryMap)
        }.flowOn(Dispatchers.IO)
            .combine(ignoringMacFlow) { myDevices, ignoringMacSet ->
                // 过滤掉正在忽略的设备
                myDevices.filterNot {
                    ignoringMacSet.contains(it.address.uppercase())
                }
            }.flowOn(Dispatchers.IO)

    private var byUnpair = false


    //点击重连返回结果
    val connectResultFlow = MutableSharedFlow<Int>()

    // 用来监控音频设备的变化
    private val audioManager by lazy {
        application.getSystemService(Application.AUDIO_SERVICE) as AudioManager
    }

    private val audioDeviceCb = object : AudioDeviceCallback() {
        override fun onAudioDevicesAdded(addedDevices: Array<out AudioDeviceInfo?>?) {
            super.onAudioDevicesAdded(addedDevices)
            launch { updateDeviceList() }
        }

        override fun onAudioDevicesRemoved(removedDevices: Array<out AudioDeviceInfo?>?) {
            super.onAudioDevicesRemoved(removedDevices)
            launch { updateDeviceList() }
        }
    }

    init {
        launch {
            audioManager.registerAudioDeviceCallback(audioDeviceCb, null)
            updateDeviceList()
        }

    }

    @SuppressLint("WrongConstant")
    private suspend fun updateDeviceList() = withContext(Dispatchers.Default) {
        logTagD(TAG, "updateDeviceList:开始更新设备列表")
        val audioDevices = audioManager.getDevices(AudioManager.GET_DEVICES_ALL)
        val btList = audioDevices.filter {
            it.isBluetoothDevice
        }.map { it.address }
        connectAudioBTMacFlow.value = btList
        logTagD(TAG, "updateDeviceList:设备列表更新完成")
    }

    /**
     * 开始扫描
     */
    fun startScan() {
        localAdapter.startScanning(true)
    }

    /**
     * 停止扫描
     */
    fun stopScan() {
        localAdapter.stopScanning()
    }

    @SuppressLint("NewApi")
    fun connect(device: CachedBluetoothDevice) {
        logTagD(TAG, "connect:${device.name}")
        val bondState = device.bondState
        if (bondState == BluetoothDevice.BOND_BONDED) {
            logTagD(TAG, "connect:已配对, 重新连接")
            device.connect(true)
        } else {
            logTagD(TAG, "connect:未配对, 开始配对")
            device.startPairing().also {
                logTagD(TAG, "connect:配对结果:$it")
            }
        }
    }

    @SuppressLint("NewApi")
    fun ignoreDevice(device: CachedBluetoothDevice) {
        logTagD(TAG, "忽略设备:${device.name}")
        val mac = device.device.address.uppercase()
        ignoringMacFlow.value = ignoringMacFlow.value + mac // 将mac地址加入到忽略列表中
        byUnpair = true
        device.unpair()
    }

    /**
     * 更新已连接蓝牙设备的信息
     */
    @SuppressLint("NewApi")
    private fun updateMyDeviceInfo(
        boundDevices: List<CachedBluetoothDevice>,
        touchPadProBatteryMap: Map<String, Int>
    ): List<BTBoundDeviceEntity> {
        return boundDevices.map { cachedDevice ->
            val mac = cachedDevice.device.address.uppercase()
            val battery =
                touchPadProBatteryMap.getOrDefault(mac, BTBoundDeviceEntity.BATTERY_LEVEL_NONE)
            logTagV(TAG, "updateMyDeviceInfo:mac:$mac, battery:$battery")
            if (cachedDevice.name == "CZUR TouchBoard Pro"
                && battery == BTBoundDeviceEntity.BATTERY_LEVEL_NONE
                && (cachedDevice.isConnected)
            ) {
                logTagW(TAG, "键盘电量没有获取到, 再更新一次电量信息")
                manualRefreshFlow.value = System.currentTimeMillis()
            }
            BTBoundDeviceEntity(battery, cachedDevice)
        }.sortedBy { it.name }
    }

    /**
     * 获取蓝牙键盘的电量
     */
    private suspend fun getTouchBoardProBatteryLevel(): Map<String, Int> =
        // 这里的电量获取方式, 是深圳给的文档中确定的
        withContext(Dispatchers.IO) {
            val deviceDir = File("sys/bus/hid/devices")
            val touchBoardProDirs = deviceDir.listFiles { file ->
                file.isDirectory && file.name.startsWith("0005:0A5C:C10A.")
            } ?: return@withContext emptyMap()

            touchBoardProDirs
                .mapNotNull {
                    val batteryDir = File(it, "power_supply")
                    if (batteryDir.exists()) batteryDir else null
                }
                .mapNotNull { batteryDir ->
                    val btDeviceDir =
                        batteryDir.listFiles()?.firstOrNull() ?: return@mapNotNull null
                    val btMacRaw =
                        btDeviceDir.name.removePrefix("hid-").removeSuffix("-battery")
                    if (btMacRaw.length != 17) return@mapNotNull null
                    val btMac = btMacRaw.uppercase()
                    val batteryFile = File(btDeviceDir, "capacity")
                    if (!batteryFile.exists()) {
                        logTagW(TAG, "蓝牙键盘电量文件不存在: ${batteryFile.absolutePath}")
                        return@mapNotNull null
                    }
                    try {
                        val battery =
                            batteryFile.readText().trim().toIntOrNull() ?: return@mapNotNull null
                        btMac to battery
                    } catch (tr: Throwable) {
                        logTagE(TAG, "获取蓝牙键盘电量失败!", tr = tr)
                        null
                    }

                }
                .associate {
                    it
                }
        }

    private inner class DockBluetoothCallback : BluetoothCallback {
        override fun onBluetoothStateChanged(bluetoothState: Int) {}

        override fun onScanningStateChanged(started: Boolean) {
            logTagD(TAG, "onScanningStateChanged:${started}")
            if (started) {
                localManager.cachedDeviceManager.clearNonBondedDevices()
            }
            isScanningFlow.value = started
        }

        override fun onDeviceAdded(cachedDevice: CachedBluetoothDevice?) {
            logTagD(TAG, "onDeviceAdded:${cachedDevice?.name}")
            allDevicesFlow.value =
                CachedDevicesWrapper(localManager.cachedDeviceManager.cachedDevicesCopy)
        }

        @SuppressLint("NewApi")
        override fun onDeviceDeleted(cachedBluetoothDevice: CachedBluetoothDevice?) {
            logTagD(TAG, "onDeviceDeleted:${cachedBluetoothDevice?.name}")
            allDevicesFlow.value =
                CachedDevicesWrapper(localManager.cachedDeviceManager.cachedDevicesCopy)
        }

        @SuppressLint("MissingPermission", "NewApi")
        override fun onDeviceBondStateChanged(
            cachedDevice: CachedBluetoothDevice?,
            bondState: Int
        ) {
            if (bondState == BluetoothDevice.BOND_NONE) {
                logTagV(TAG, "清理未绑定的设备")
                localManager.cachedDeviceManager.clearNonBondedDevices()
            }

            /// 如果连接上了蓝牙，则获取蓝牙配对类型
            if (bondState == BluetoothDevice.BOND_BONDED) {
                val deviceClass = cachedDevice?.device?.bluetoothClass?.deviceClass
                logTagD(TAG, "获取蓝牙配对类型 deviceClass:${deviceClass}")
            }

            logTagD(
                TAG,
                "onDeviceBondStateChanged:${cachedDevice?.name} - ${bondState.toBoundStateStr()}"
            )
            if (byUnpair) {
                logTagI(TAG, "onDeviceBondStateChanged:手动忽略设备, 清理未绑定的设备")
                byUnpair = false
                localManager.cachedDeviceManager.clearNonBondedDevices()
            }
            val deviceMac = cachedDevice?.device?.address?.uppercase().orEmpty()
            if (ignoringMacFlow.value.contains(deviceMac)) {
                logTagD(TAG, "${cachedDevice?.name}(${deviceMac}) 绑定状态改变, 从忽略列表中移除")
                ignoringMacFlow.value =
                    ignoringMacFlow.value - deviceMac
            }
            allDevicesFlow.value =
                CachedDevicesWrapper(localManager.cachedDeviceManager.cachedDevicesCopy)
        }

        @SuppressLint("NewApi")
        override fun onConnectionStateChanged(cachedDevice: CachedBluetoothDevice?, state: Int) {
            logTagD(
                TAG,
                "onConnectionStateChanged:${cachedDevice?.name} - ${state.toConnectStateStr()}"
            )
            if (cachedDevice != null && state == BluetoothProfile.STATE_CONNECTED) {
                toShowToast(cachedDevice)
            }
            val devices = localManager.cachedDeviceManager.cachedDevicesCopy
            allDevicesFlow.value = CachedDevicesWrapper(devices)

            launch {
                connectResultFlow.emit(state)
            }
        }
    }

    //音频设备连接成功toast提示
    private fun toShowToast(cachedDevice: CachedBluetoothDevice) {
        if (cachedDevice.isSpeaker || cachedDevice.isHeadPhones) {
            toast(R.string.str_bluetooth_connect_success_toast)
        }
    }

    /**
     * 包装一下, 让flow每次都可以刷新
     */
    private data class CachedDevicesWrapper(
        val cachedDevices: Collection<CachedBluetoothDevice>,
        val createTime: Long = System.currentTimeMillis()
    )

    override fun onCleared() {
        super.onCleared()
        logTagD(TAG, "BTDockViewModel onCleared")
        localManager.eventManager.unregisterCallback(dockBluetoothCallback)
        audioManager.unregisterAudioDeviceCallback(audioDeviceCb)
    }

    private fun Int.toBoundStateStr(): String {
        return when (this) {
            BluetoothDevice.BOND_NONE -> "未绑定"
            BluetoothDevice.BOND_BONDING -> "绑定中"
            BluetoothDevice.BOND_BONDED -> "已绑定"
            else -> "未知"
        }
    }

    private fun Int.toConnectStateStr(): String {
        return when (this) {
            BluetoothProfile.STATE_DISCONNECTED -> "未连接"
            BluetoothProfile.STATE_CONNECTING -> "连接中"
            BluetoothProfile.STATE_CONNECTED -> "已连接"
            BluetoothProfile.STATE_DISCONNECTING -> "断开连接中"
            else -> "未知"
        }
    }
}