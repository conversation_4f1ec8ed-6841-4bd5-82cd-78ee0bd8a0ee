package com.czur.starry.device.settings.ui.cameraandmic

import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentAirControlBinding

/**
 * Created by 陈丰尧 on 2025/6/25
 */
class AirControlFragment : BaseBindingMenuFragment<FragmentAirControlBinding>() {
    override fun FragmentAirControlBinding.initBindingViews() {
        launch {
            val enable = SettingUtil.CameraAndMicSetting.getAirControlEnable()
            airControlEnableCb.setChecked(enable, false)
        }

        airControlEnableCb.setOnCheckedChangeListener { isOn: Boolean, fromUser: Boolean ->
            if (fromUser) {
                launch {
                    SettingUtil.CameraAndMicSetting.setAirControlEnable(isOn)
                }
            }
        }
    }
}