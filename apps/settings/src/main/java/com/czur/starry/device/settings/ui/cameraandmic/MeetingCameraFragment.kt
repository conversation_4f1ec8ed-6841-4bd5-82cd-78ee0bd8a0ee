package com.czur.starry.device.settings.ui.cameraandmic

import android.content.Intent
import android.os.Bundle
import com.bumptech.glide.Glide
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.otalib.OTAHandler.cameraVersionCheck
import com.czur.starry.device.otalib.OTAHandler.currentCameraVersion
import com.czur.starry.device.otalib.OTAHandler.newCameraVersion
import com.czur.starry.device.otalib.OTAHandler.newCameraVersionStatusLive
import com.czur.starry.device.otalib.OTAHandler.systemTrackModeStatus
import com.czur.starry.device.otalib.OTAHandler.systemTrackModeStatusLive
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentMeetingCameraBinding
import com.czur.starry.device.settings.ui.cameraandmic.camera.CameraUpdateActivity
import com.czur.uilib.choose.CZCheckBox
import java.io.File
import kotlin.math.ceil

/**
 * Created by 陈丰尧 on 2023/4/10
 */
private const val TAG = "MeetingCameraFragment"
const val TOAST_DURATION = 4 * ONE_SECOND
const val UPDATE_DELAY_TIME = "delayTime"
const val UPDATE_TOTAL_TIME = "updateTime"

class MeetingCameraFragment : BaseBindingMenuFragment<FragmentMeetingCameraBinding>() {

    private val systemManager by lazy { SystemManagerProxy() }

    private lateinit var currentMode: SystemManagerProxy.TrackMode
    private var showToastTime = 0L
    private var delayTime = 1000L
    private var totalTime = 2 //默认升级时间2分钟

    private val showAutoTrack: Boolean by lazy {
        isAutoTrackCanEnable()
    }


    override fun FragmentMeetingCameraBinding.initBindingViews() {
        val gifRes = when(Constants.starryHWInfo.series){
            Q1Series -> R.drawable.img_meeting_camera_smart
            Q2Series -> R.drawable.img_meeting_camera_smart_q2
            StudioSeries -> R.drawable.img_meeting_camera_smart_studio
        }
        Glide.with(this@MeetingCameraFragment)
            .asGif()
            .load(gifRes)
            .into(meetingCameraSmartIv)

        when(Constants.starryHWInfo.series){
            Q1Series -> meetingCameraFullIv.setImageResource(R.drawable.img_meeting_camera_full)
            Q2Series -> meetingCameraFullIv.setImageResource(R.drawable.img_meeting_camera_full_q2)
            StudioSeries -> meetingCameraFullIv.setImageResource(R.drawable.img_meeting_camera_full_studio)
        }


        fullScreenCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation
        smartScreenCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation
//        autoTrackCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation
        // 完整画面
        fullScreenCb.setOnCheckedChangeListener { isOn, fromUser ->
            if (fromUser && isOn) {
                systemManager.setTrackMode(SystemManagerProxy.TrackMode.TRACK_MODE_OFF)
                updateUiByTrackMode(systemManager.getTrackMode())
            }
        }
        // 智能取景
        smartScreenCb.setOnCheckedChangeListener { isOn, fromUser ->
            if (fromUser && isOn) {
                systemManager.setTrackMode(SystemManagerProxy.TrackMode.TRACK_MODE_VISION)
                updateUiByTrackMode(systemManager.getTrackMode())
            }
        }
        // 智能取景+声源定位
        autoTrackCb.setOnCheckedChangeListener { isOn, fromUser ->
            if (fromUser) {
                if (isOn) {
                    systemManager.setTrackMode(SystemManagerProxy.TrackMode.TRACK_MODE_VISION_AND_DOA)
                } else {
                    systemManager.setTrackMode(SystemManagerProxy.TrackMode.TRACK_MODE_VISION)
                }
                updateUiByTrackMode(systemManager.getTrackMode())
            }
        }
        meetingCameraSmartIv.setOnDebounceClickListener {
            if (!autoTrackCb.isChecked()) {
                if (showAutoTrack) {
                    logTagV(TAG, "同时打开 智能取景+声源定位")
                    systemManager.setTrackMode(SystemManagerProxy.TrackMode.TRACK_MODE_VISION_AND_DOA)
                } else {
                    logTagV(TAG, "仅打开 智能取景")
                    systemManager.setTrackMode(SystemManagerProxy.TrackMode.TRACK_MODE_VISION)
                }
                updateUiByTrackMode(systemManager.getTrackMode())
            }
        }

        autoTrackCb.gone(!showAutoTrack)
        autoTrackTv.gone(!showAutoTrack)

        newCameraVersionStatusLive.observe(viewLifecycleOwner) {
            logTagD(TAG, "======newCameraVersion=$it")
            val currentV = currentCameraVersion
            val isShow = it != "null" && it != currentV
            if (isShow) {
                caNewVersionTv.text = getString(
                    R.string.latest_version,
                    formatVersion(newCameraVersion)
                )
                currentVersionTv.text = getString(
                    R.string.current_version,
                    formatVersion(currentV)
                )
                updateGroup.show()
            } else {
                updateGroup.gone()
            }
        }


        updateBtn.setOnDebounceClickListener {
            showWarningDialog()
        }

        systemTrackModeStatusLive.observe(viewLifecycleOwner) {
            if (it) {
                updateUiByTrackMode(systemManager.getTrackMode())
                systemTrackModeStatus = false
            }
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        val currentTrackMode = systemManager.getTrackMode()
        currentMode = currentTrackMode
        binding.updateUiByTrackMode(currentTrackMode)
        cameraVersionCheck = true
    }


    private fun showWarningDialog() {
        calculateDelayTimeBasedOnFileSize()
        DoubleBtnCommonFloat(
            content = getString(
                R.string.camera_upgrade_dialog_warning,
                totalTime
            )
        ) { doubleCommonFloat, position ->
            if (position == 0) {
                doubleCommonFloat.dismiss()
            } else {
                doubleCommonFloat.dismiss()
                val intent = Intent(requireContext(), CameraUpdateActivity::class.java)
                intent.putExtra(UPDATE_DELAY_TIME, delayTime)
                intent.putExtra(UPDATE_TOTAL_TIME, totalTime)
                startActivity(intent)
            }
        }.show()
    }

    /**
     * 计算升级大概时间
     */
    private fun calculateDelayTimeBasedOnFileSize() {
        val file = File(Constants.PATH_SDCARD_OTA_CAMERA)
        val megabytes = file.length() / 1024 / 1024
        delayTime = megabytes * 5 * ONE_SECOND / 100
        totalTime = ceil(delayTime * 100.toDouble() / 1000 / 60).toInt()
    }

    /**
     * 通过追踪模式更新UI
     */
    private fun FragmentMeetingCameraBinding.updateUiByTrackMode(trackMode: SystemManagerProxy.TrackMode) {
        if (currentMode != trackMode && System.currentTimeMillis() - showToastTime > TOAST_DURATION) {
            toast(R.string.toast_meeting_camera_change_hint)
            showToastTime = System.currentTimeMillis()
        }
        currentMode = trackMode
        when (trackMode) {
            SystemManagerProxy.TrackMode.TRACK_MODE_OFF -> {
                // 关闭追踪模式
                fullScreenCb.setChecked(checked = true, useAnim = true)
                smartScreenCb.setChecked(checked = false, useAnim = true)
                autoTrackCb.setChecked(checked = false, useAnim = true)
            }

            SystemManagerProxy.TrackMode.TRACK_MODE_VISION -> {
                // 智能取景
                fullScreenCb.setChecked(checked = false, useAnim = true)
                smartScreenCb.setChecked(checked = true, useAnim = true)
                autoTrackCb.setChecked(checked = false, useAnim = true)
            }

            SystemManagerProxy.TrackMode.TRACK_MODE_VISION_AND_DOA -> {
                // 智能取景+声源定位
                fullScreenCb.setChecked(checked = false, useAnim = true)
                smartScreenCb.setChecked(checked = true, useAnim = true)
                autoTrackCb.setChecked(checked = true, useAnim = true)
            }
        }
    }

    /**
     * 是否可以开启自动追踪
     */
    private fun isAutoTrackCanEnable() = when (val s = Constants.starryHWInfo.series) {
        is Q1Series -> when (s.model) {
            StarryModel.Q1Model.Q1, StarryModel.Q1Model.Q1S, StarryModel.Q1Model.Q1Pro -> false
            StarryModel.Q1Model.Q1SPlus, StarryModel.Q1Model.Q1SPro -> true
        }

        is Q2Series -> when (s.model) {  // Q2 Q2Pro 不支持
            StarryModel.Q2Model.Q2 -> false
            StarryModel.Q2Model.Q2Pro -> false
            else -> true
        }

        is StudioSeries -> when (s.model) {
            StarryModel.StudioModel.Studio,
            StarryModel.StudioModel.StudioPro,
            StarryModel.StudioModel.StudioS,
            StarryModel.StudioModel.StudioSPro -> false

            else -> true
        }
    }.also {
        logTagI(
            TAG,
            "isAutoTrackCanEnable: $it deviceTypeName=${Constants.starryHWInfo.series.model}"
        )
    }

    private fun formatVersion(version: String): String {
        val formatted = StringBuilder()
        formatted.append("v ")
        for (i in version.indices) {
            formatted.append(version[i])
            if (i != version.length - 1) {
                formatted.append(".")
            }
        }
        return formatted.toString()
    }

}