package com.czur.starry.device.settings.manager

import android.os.storage.StorageManager
import android.os.storage.VolumeInfo
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.view.getVolumes
import com.czur.starry.device.settings.utils.MEMORY_32G
import com.czur.starry.device.settings.utils.MEMORY_64G
import java.io.File

/**
 * Created by 陈丰尧 on 3/8/21
 */
class PrivateStorageInfo private constructor(val freeBytes: Long, val totalBytes: Long) {

    companion object {
        const val TAG = "PrivateStorageInfo"
        fun getInstance(): PrivateStorageInfo {
            val sm = StorageManagerVolumeProvider
            val totalInternalStorage: Long = sm.getPrimaryStorageSize()
            var privateFreeBytes: Long = 0
            var privateTotalBytes: Long = 0
            for (info in sm.getVolumes()) {
                val path: File = info.getPath()
                if (info.getType() != VolumeInfo.TYPE_PRIVATE || path == null) {
                    continue
                }
                privateTotalBytes += getTotalSize(info, totalInternalStorage)
                privateFreeBytes += path.getFreeSpace()
            }
            var totalSize = 0L
            if (privateTotalBytes < MEMORY_32G) {
                totalSize = MEMORY_32G
            } else if (privateTotalBytes > MEMORY_32G && privateTotalBytes < MEMORY_64G) {
                totalSize = MEMORY_64G
            } else {
                totalSize = privateTotalBytes
            }
            val mFreeMem = globalAppCtx.filesDir.freeSpace

            return PrivateStorageInfo(mFreeMem, totalSize)
        }

        private fun getTotalSize(info: VolumeInfo, totalInternalStorage: Long): Long {
            return if (info.getType() == VolumeInfo.TYPE_PRIVATE
                && info.getFsUuid() == StorageManager.UUID_PRIVATE_INTERNAL
                && totalInternalStorage > 0
            ) {
                totalInternalStorage
            } else {
                val path = info.getPath()
                if (path == null) {
                    // Should not happen, caller should have checked.
                    logTagE(TAG, "info's path is null on getTotalSize(): $info")
                    return 0
                }
                path.totalSpace
            }
        }
    }


}

private object StorageManagerVolumeProvider {
    private val storageManager by lazy {
        CZURAtyManager.appContext.getSystemService(StorageManager::class.java)
    }

    fun getPrimaryStorageSize(): Long {
        return storageManager.getPrimaryStorageSize()
    }

    fun getVolumes(): List<VolumeInfo> {
        return storageManager.getVolumes()
    }
}