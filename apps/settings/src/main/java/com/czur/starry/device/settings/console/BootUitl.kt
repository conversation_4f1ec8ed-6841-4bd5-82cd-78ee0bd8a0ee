package com.czur.starry.device.settings.console

import android.content.Context
import android.content.Intent
import com.czur.czurutils.global.globalAppCtx
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel

/**
 * Created by 陈丰尧 on 2024/7/1
 */
class BootUtil {
    companion object {
        // 厂测包名
        private const val PKG_NAME_DEVICE_TEST = "com.DeviceTest"
        private const val CLZ_NAME_DEVICE_TEST = "com.DeviceTest.DeviceTest"
        // 厂测包名
        private const val PKG_NAME_DEVICE_TEST_ANDROID14 = "com.czur.starry.devicetest"
        private const val CLZ_NAME_DEVICE_TEST_ANDROID14 = "com.czur.starry.devicetest.BootActivity"

        // 老化包名
        private const val PKG_NAME_STRESS_TEST = "com.cghs.stresstest"
        private const val CLZ_NAME_STRESS_TEST = "com.cghs.stresstest.StressTestActivity"

        // 日志收集包名
        private const val PKG_NAME_DIAGNOSIS = "com.czur.starry.device.diagnosis"
        private const val CLZ_NAME_DIAGNOSIS = "com.czur.starry.device.diagnosis.MainActivity"

        // 原生Setting
        private const val PKG_NAME_ANDROID_SETTING = "com.android.settings"
        private const val CLZ_NAME_ANDROID_SETTING = "com.android.settings.Settings"
    }

    /**
     * 启动厂测
     */
    fun bootDeviceTest(context: Context = globalAppCtx) =
        when (Constants.starryHWInfo.series.model) {
            StarryModel.Q1Model.Q1, StarryModel.Q1Model.Q1S, StarryModel.Q1Model.Q1Pro,
            StarryModel.Q1Model.Q1SPlus, StarryModel.Q1Model.Q1SPro -> bootApp(
                PKG_NAME_DEVICE_TEST,
                CLZ_NAME_DEVICE_TEST,
                context
            )
            else -> bootApp(PKG_NAME_DEVICE_TEST_ANDROID14, CLZ_NAME_DEVICE_TEST_ANDROID14, context)
        }



    /**
     * 启动老化
     */
    fun bootStressTest(context: Context = globalAppCtx) =
        when (Constants.starryHWInfo.series.model) {
            StarryModel.Q1Model.Q1, StarryModel.Q1Model.Q1S, StarryModel.Q1Model.Q1Pro,
            StarryModel.Q1Model.Q1SPlus, StarryModel.Q1Model.Q1SPro -> bootApp(
                PKG_NAME_STRESS_TEST,
                CLZ_NAME_STRESS_TEST,
                context
            )
            else -> bootApp(PKG_NAME_DEVICE_TEST_ANDROID14, CLZ_NAME_DEVICE_TEST_ANDROID14, context)
        }

    /**
     * 启动日志收集
     */
    fun bootDiagnosis(context: Context = globalAppCtx) =
        bootApp(PKG_NAME_DIAGNOSIS, CLZ_NAME_DIAGNOSIS, context)

    /**
     * 启动原生Setting
     */
    fun bootAndroidSetting(context: Context = globalAppCtx) =
        bootApp(PKG_NAME_ANDROID_SETTING, CLZ_NAME_ANDROID_SETTING, context)


    private fun bootApp(pkgName: String, clzName: String, context: Context) {
        val intent = Intent().apply {
            setClassName(pkgName, clzName)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        context.startActivity(intent)
    }
}