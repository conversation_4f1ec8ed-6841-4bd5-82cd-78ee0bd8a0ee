<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/preview_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/im_dis_view"
        android:scaleType="fitXY"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:id="@+id/titleBarPre"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:layout_marginLeft="60px"
        android:layout_marginTop="51px"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="60px"
            android:layout_height="60px"
            android:src="@drawable/ic_preview_back"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="60px"
            android:layout_marginLeft="30px"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="@string/focus_back"
            android:textColor="@color/white"
            android:textSize="36px"
            android:textStyle="bold" />

    </LinearLayout>

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/btDelete"
        android:layout_width="200px"
        android:layout_height="80px"
        android:layout_marginRight="30px"
        android:text="@string/wallpaper_bt_delete"
        android:textSize="30px"
        app:baselib_theme="dark3"
        app:layout_constraintRight_toLeftOf="@+id/btPlayOrStop"
        app:layout_constraintTop_toTopOf="@+id/btPlayOrStop" />


    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/btPlayOrStop"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginRight="60px"
        android:layout_marginBottom="60px"
        android:text="@string/btn_use_now"
        android:textSize="30px"
        app:baselib_theme="white2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>