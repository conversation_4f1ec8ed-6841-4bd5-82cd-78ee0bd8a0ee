package com.czur.starry.device.localmeetingrecord.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.os.Debug
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.utils.appContext
import kotlinx.coroutines.*
import java.io.File

/**
 * 性能监控工具
 * 用于监控录制过程中的CPU和内存使用情况
 */
object PerformanceMonitor {
    private const val TAG = "PerformanceMonitor"
    
    private var monitorJob: Job? = null
    private var isMonitoring = false
    
    data class PerformanceData(
        val timestamp: Long,
        val cpuUsage: Float,
        val memoryUsage: Long,
        val availableMemory: Long,
        val androidVersion: Int
    )
    
    private val performanceHistory = mutableListOf<PerformanceData>()
    
    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (isMonitoring) {
            logTagI(TAG, "性能监控已在运行中")
            return
        }
        
        isMonitoring = true
        performanceHistory.clear()
        
        logTagI(TAG, "开始性能监控 - Android ${Build.VERSION.SDK_INT}")
        
        monitorJob = CoroutineScope(Dispatchers.IO).launch {
            while (isMonitoring) {
                try {
                    val performanceData = collectPerformanceData()
                    performanceHistory.add(performanceData)
                    
                    logTagD(TAG, "性能数据: CPU=${performanceData.cpuUsage}%, " +
                            "内存=${performanceData.memoryUsage / 1024 / 1024}MB, " +
                            "可用内存=${performanceData.availableMemory / 1024 / 1024}MB")
                    
                    delay(1000) // 每秒采集一次
                } catch (e: Exception) {
                    logTagD(TAG, "性能监控异常: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitorJob?.cancel()
        
        logTagI(TAG, "停止性能监控")
        generatePerformanceReport()
    }
    
    /**
     * 收集性能数据
     */
    private fun collectPerformanceData(): PerformanceData {
        val activityManager = appContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val cpuUsage = getCpuUsage()
        val memoryUsage = getMemoryUsage()
        
        return PerformanceData(
            timestamp = System.currentTimeMillis(),
            cpuUsage = cpuUsage,
            memoryUsage = memoryUsage,
            availableMemory = memoryInfo.availMem,
            androidVersion = Build.VERSION.SDK_INT
        )
    }
    
    /**
     * 获取CPU使用率
     */
    private fun getCpuUsage(): Float {
        return try {
            val statFile = File("/proc/stat")
            if (statFile.exists()) {
                val lines = statFile.readLines()
                if (lines.isNotEmpty()) {
                    val cpuLine = lines[0]
                    val values = cpuLine.split("\\s+".toRegex())
                    if (values.size >= 5) {
                        val idle = values[4].toLong()
                        val total = values.drop(1).take(7).sumOf { it.toLong() }
                        val usage = ((total - idle).toFloat() / total.toFloat()) * 100
                        usage.coerceIn(0f, 100f)
                    } else 0f
                } else 0f
            } else 0f
        } catch (e: Exception) {
            0f
        }
    }
    
    /**
     * 获取内存使用量
     */
    private fun getMemoryUsage(): Long {
        return try {
            val memoryInfo = Debug.MemoryInfo()
            Debug.getMemoryInfo(memoryInfo)
            memoryInfo.totalPss * 1024L // 转换为字节
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * 生成性能报告
     */
    private fun generatePerformanceReport() {
        if (performanceHistory.isEmpty()) {
            logTagI(TAG, "没有性能数据可生成报告")
            return
        }
        
        val avgCpu = performanceHistory.map { it.cpuUsage }.average()
        val maxCpu = performanceHistory.maxOfOrNull { it.cpuUsage } ?: 0f
        val avgMemory = performanceHistory.map { it.memoryUsage }.average()
        val maxMemory = performanceHistory.maxOfOrNull { it.memoryUsage } ?: 0L
        
        val androidVersion = performanceHistory.firstOrNull()?.androidVersion ?: Build.VERSION.SDK_INT
        
        logTagI(TAG, "=== 性能监控报告 ===")
        logTagI(TAG, "Android版本: $androidVersion")
        logTagI(TAG, "监控时长: ${performanceHistory.size}秒")
        logTagI(TAG, "平均CPU使用率: ${String.format("%.2f", avgCpu)}%")
        logTagI(TAG, "最大CPU使用率: ${String.format("%.2f", maxCpu)}%")
        logTagI(TAG, "平均内存使用: ${String.format("%.2f", avgMemory / 1024 / 1024)}MB")
        logTagI(TAG, "最大内存使用: ${String.format("%.2f", maxMemory / 1024.0 / 1024)}MB")
        logTagI(TAG, "==================")
        
        // 如果是Android 14，特别标注优化效果
        if (androidVersion >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            logTagI(TAG, "注意: 此为Android 14+优化版本的性能数据")
            logTagI(TAG, "优化项: 20fps帧率, VBR编码, 2秒I帧间隔, 33ms输入延迟")
        }
    }
    
    /**
     * 获取当前性能快照
     */
    fun getPerformanceSnapshot(): PerformanceData? {
        return if (isMonitoring && performanceHistory.isNotEmpty()) {
            performanceHistory.last()
        } else {
            null
        }
    }
    
    /**
     * 清除性能历史数据
     */
    fun clearHistory() {
        performanceHistory.clear()
        logTagD(TAG, "性能历史数据已清除")
    }
}
