package com.czur.starry.device.localmeetingrecord

import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.hardware.Camera
import android.media.AudioManager
import android.os.Bundle
import android.os.IBinder
import android.view.KeyEvent
import android.view.View
import androidx.activity.viewModels
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.czer.starry.device.meetlib.MeetingHandler
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logStackTrace
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_CODE_BTB_SEAT
import com.czur.starry.device.baselib.common.KEY_TOUCH_PAD_DOCK_WAKE
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.utils.AudioUtil
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.ProcessName
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.getAppNamesByPkges
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.NormDialog
import com.czur.starry.device.baselib.view.floating.FloatShowMode
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_X
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_Y
import com.czur.starry.device.localmeetingrecord.Config.DEFAULT_RECORD_MODE
import com.czur.starry.device.localmeetingrecord.Config.RENAME_DIALOG_SHOWED_ACTION
import com.czur.starry.device.localmeetingrecord.Config.START_MUXER_ACTION
import com.czur.starry.device.localmeetingrecord.Config.TIME_NO_SPACE_REC
import com.czur.starry.device.localmeetingrecord.RecordModel.AUDIO_MODE
import com.czur.starry.device.localmeetingrecord.RecordModel.CAMERA_MODE
import com.czur.starry.device.localmeetingrecord.RecordModel.SCREEN_MODE
import com.czur.starry.device.localmeetingrecord.RecordModel.VIDEO_MODE
import com.czur.starry.device.localmeetingrecord.broadcast.addMainBroadcastReceiver
import com.czur.starry.device.localmeetingrecord.databinding.ActivityMainBinding
import com.czur.starry.device.localmeetingrecord.monitor.CameraWrapper
import com.czur.starry.device.localmeetingrecord.monitor.MuxerManager
import com.czur.starry.device.localmeetingrecord.services.FloatImageWindowService
import com.czur.starry.device.localmeetingrecord.services.RecordAudioFloatWindowService
import com.czur.starry.device.localmeetingrecord.services.RecordFloatTimeWindowService
import com.czur.starry.device.localmeetingrecord.services.RecordScreenFloatWindowService
import com.czur.starry.device.localmeetingrecord.services.RecordingFrontService
import com.czur.starry.device.localmeetingrecord.setting.setBiCubicState
import com.czur.starry.device.localmeetingrecord.utils.PreviewHelper
import com.czur.starry.device.localmeetingrecord.widget.CameraView
import com.czur.starry.device.localmeetingrecord.widget.RenameDialogActivity
import com.czur.starry.device.localmeetingrecord.widget.SoundWaveView
import com.czur.starry.device.sharescreen.esharelib.EshareByomRunningStatusCallBack
import com.czur.starry.device.sharescreen.esharelib.EshareByomStatusModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.InternalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.system.exitProcess


/**
 * Created by 陈丰尧 on 2022/8/5
 */
class MainActivity : CZViewBindingAty<ActivityMainBinding>(), LifecycleEventObserver {
    companion object {
        private const val TAG = "localmeetingrecord--MainActivity"
        private const val CHECK_TIME_THRESHOLD = ONE_SECOND
        var renameDialogShowing = false//正在显示
        var renameDialogShowed = false//刚刚显示过,处理完成以后置为false
    }

    enum class ByomForceStopType {
        initCamera,
        initCameraInResume,
        startOrStop;
    }

    private val mainViewModel: MainViewModel by viewModels()

    private var audioUtil = AudioUtil()

    var recordFloatScreenWindowService: RecordScreenFloatWindowService? = null

    var recordFloatAudioWindowService: RecordAudioFloatWindowService? = null


    var recordFrontService: RecordingFrontService? = null

    private var currentRecordMode = VIDEO_MODE

    /**
     * 语音执行的模式标记
     */
    private var targetRecordMode = CAMERA_MODE

    var screenFloatx: Int? = -1
    var screenFloaty: Int? = -1
    var audioFloatx: Int? = AUDIO_FLOAT_X
    var audioFloaty: Int? = AUDIO_FLOAT_Y

    var isStopping = false

    var isFirstIn = true // oncreate中是否是第一次进入

    var surfaceViewCreated = false // surfaceview是否创建完成

    private var stopFiveMinutesLaunch: Job? = null
    private var delayMoveToBackLaunch: Job? = null // 录屏模式,延迟移动到后台,在录制钱500毫秒返回到后台,防止录制到黑色背景
    private var resumeInitCameraLaunch: Job? = null
    private var startRecordLaunch: Job? = null
    private var stopRecordDialog: DoubleBtnCommonFloat? = null

    var onDismissListenerFloat: DoubleBtnCommonFloat? = null

    var backFromRenameDialog = false

    var needInitializeCameraWhenResume = false // 是否需要尝试重新初始化相机,释放了相机资源和相机error,都需要重新初始化,防止多次同时初始化

    var preventBackpress = false
    private val shareByomModel: EshareByomStatusModel by viewModels()

    private var isShowingByomForceStopDialog = false
    override fun AtyParams.initAtyParams() {
        keepScreenOn = true
        skipAndFinish = false

        onScreenOffListener = ::onScreenOFF
        onTaskRemovedListener = ::onTaskRemoved
    }

    override fun ActivityMainBinding.initBindingViews() {
        background.visibility = View.VISIBLE
        MeetingHandler.localMeetingRecording = false
        MeetingHandler.localMeetingVideoRecording = false
        MeetingHandler.localMeetingRecordCanEShare = true

        // 如果有触控屏就隐藏掉返回键
        backIv.gone(Constants.starryHWInfo.hasTouchScreen)


        lifecycle.addObserver(this@MainActivity)
        // 先按照默认状态进行初始化View
        timeWaterMarkCb.isChecked = mainViewModel.enableTimeWaterMark

        // 初始化时设置混合器启动监听器
        setupMixerStartListener()

        binding.surfaceView.onReadyCallback(
            readyCallback = {
                logTagI(TAG, "surfaceView_readyCallback123")
                surfaceViewCreated = true
                if (mainViewModel.isGadgetStreamInUse()) {
                    logTagI(TAG, "USB外设模式使用中")
                    // USB外设模式的冲突只可能在应用启动时发生,在应用运行过程中发生冲突时, 底层会直接杀掉应用
                    // 并且如果USB外设模式发生冲突了, 那么表示其他应用一定没有占用资源
                    showUSBForceStopDialog()
                } else if (shareByomModel.isEshareByomRunning()) {
                    showByomForceStopDialog(ByomForceStopType.initCamera)
                } else {
                    if (isCameraAndeAudioRecourseOccupy()) {
                        val processNames = mainViewModel.getUseMicProcessName()
                        showForceStopDialog(
                            R.string.dialog_mic_occupy_hint_init,
                            processNames
                        ) {
                            if (it) {
                                initCamera(
                                    binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                                    Config.SRC_VIDEO_HEIGHT_1080, needRelease = true
                                )
                            } else {
                                finish()
                            }
                        }


                    } else {
                        initCamera(
                            binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                            Config.SRC_VIDEO_HEIGHT_1080, needRelease = true
                        )
                    }
                }

            },
            displayFrameCallback = {})

        launch {
            // 时间水印
            timeWaterMarkCb.isChecked = Config.DEF_ENABLE_TIME_WATERMARK
            timeWaterMarkCb.onCheckChangeListener = {
                mainViewModel.enableTimeWaterMark = it
                whetherShowRecordScreenCurrentTimeFloat()
            }

            // 同时录像
            withVideoIv.onCheckChangeListener = {
                mainViewModel.enableWithVideoMark = it
                changeRecordScreenBg(it)
            }

            // 同时录音
            withAudioIv.onCheckChangeListener = {
                mainViewModel.enableWithAudioMark = it
                mainViewModel.enableWithSubmixAudioMark = it
            }

            // 视频镜像
            flipTheLensCb.onCheckChangeListener = {
                val rotation = if (it) {
                    mainViewModel.enableFlipLens = false
                    0f
                } else {
                    mainViewModel.enableFlipLens = true
                    180f
                }
                surfaceView.rotationY = rotation
                surfaceView.translationY = -1F
                launch {
                    delay(10)
                    surfaceView.translationY = 0F
                }
            }

        }
//        startOrStopIv.isClickable = false
        startOrStopIv.isEnabled = false
//        startOrStopIv.isFocusable = false
        /**
         * 开始或停止按钮
         */
        startOrStopIv.setOnDebounceClickListener {
            launch {
                startOrStopRecord()
            }
        }

        screenRecordingLayout.stopScreenRecordingIv.setOnDebounceClickListener {
            startOrStopIv.performClick()
        }


        launch {
            mainViewModel.playControlBtnClickable.collect {
                startOrStopIv.isClickable = it
                startOrStopIv.isEnabled = it
                startOrStopIv.isFocusable = it

                pauseOrResumeIv.isClickable = it
                pauseOrResumeIv.isEnabled = it
                pauseOrResumeIv.isFocusable = it

                screenRecordingLayout.stopScreenRecordingIv.isClickable = it
                screenRecordingLayout.stopScreenRecordingIv.isEnabled = it
                screenRecordingLayout.stopScreenRecordingIv.isFocusable = it

                screenRecordingLayout.controlRecordingIv.isClickable = it
                screenRecordingLayout.controlRecordingIv.isEnabled = it
                screenRecordingLayout.controlRecordingIv.isFocusable = it
            }
        }

        pauseOrResumeIv.setOnDebounceClickListener {
            controlBtnClick()
        }
        screenRecordingLayout.controlRecordingIv.setOnDebounceClickListener {
            controlBtnClick()
        }



        backIv.setOnDebounceClickListener {
            backIvClick(true)
        }

        launch {
            mainViewModel.screenRecordStateFlow.collect {
                if ("start" == it) {
                    startRecordScreen()
                }

            }
        }

        repeatCollectOnCreate(App.instance.finishAppFlow) {
            logTagV(TAG, "finishAppFlow:$it")
            unbindAllFrontService()
            delay(it)
            releaseResourcesAndFinish()
        }

        waveView.setVolumeListener(object : SoundWaveView.SoundWaveViewListener {
            override fun getVolume(): Int {
                val last = mainViewModel.audioWaveList.last();
                return last
            }
        })

        waveView.startAnim()
        waveView.pauseAnim()
        val intent = Intent(this@MainActivity, RecordingFrontService::class.java)
        bindService(intent, recordFrontServiceConnection, BIND_AUTO_CREATE)

        CameraWrapper.cameraListener = object : CameraWrapper.CameraListener {
            override fun onCameraErrorStatus(status: Int) {
                logTagD(TAG, "onError: 相机服务挂了?$status")

                when (status) {
                    Camera.CAMERA_ERROR_UNKNOWN,
                    Camera.CAMERA_ERROR_SERVER_DIED -> {
                        logTagD(TAG, "onError: 相机服务挂了")

                        if (isStopping) {
                            return
                        }
                        if (mainViewModel.recStatus == RecordState.STOP
                            || mainViewModel.recStatus == RecordState.PREPARE
                        ) {//未在录制情况弹窗
                            showErrorCameraDialog(R.string.toast_init_camera_fail_100)
                        } else {//录制情况取消录制
                            stopRecordAndFinish()
                        }
                    }

                    else -> {
                        if (mainViewModel.recStatus == RecordState.STOP
                            || mainViewModel.recStatus == RecordState.PREPARE
                        ) {//未在录制情况弹窗
                            needInitializeCameraWhenResume = true
                        } else {//录制情况取消录制
                            stopRecordAndFinish()
                        }
                    }
//                    Camera.CAMERA_ERROR_EVICTED,
//                    Camera.CAMERA_ERROR_DISABLED,
//                    -> {
//
//                    }
                }
            }
        }

        slideSelectTabView.beforeAnimListener = {
            mainViewModel.savePreviewBitmap()
        }

        launch {
            //语音停止录制
            mainViewModel.currentStopFlow.collect { isStop ->
                if (isStop) {
                    stopRecord()
                }
            }
        }

    }

    /**
     * 设置混合器启动监听器
     * 在每次开始录制前调用此方法重新设置监听器
     */
    private fun setupMixerStartListener() {
        logTagD(TAG, "设置混合器启动监听器")
        MuxerManager.instance.setOnMixerStartListener(object :
            MuxerManager.Companion.OnMixerStartListener {
            override fun onMixerStarted() {
                logTagD(TAG, "收到混合器启动通知")
                mainViewModel.hasStartMuxer()
                launch {
                    delay(1200)
                    mainViewModel.playControlBtnClickable.emit(true)
                }
            }
        })
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    /**
     * 处理传过来的页面信息
     */
    private fun handleIntent(intent: Intent) {
        mainViewModel.handleIntent(intent)
    }

    /**
     * 切换屏幕录制状态时的背景
     * 如果没有开启录像, 则不显示摄像头预览
     */
    private fun changeRecordScreenBg(useCamera: Boolean) {
        binding.flipTheLensGroup.visibility = if (useCamera) View.VISIBLE else View.GONE
        if (useCamera) {
            initCamera(
                binding.surfaceView,
                Config.SRC_VIDEO_WIDTH_1080,
                Config.SRC_VIDEO_HEIGHT_1080,
                needShowCoverLayout = true
            )
            binding.background.setBackgroundColor(resources.getColor(R.color.black))
            binding.surfaceView.show()
        } else {
            binding.background.setBackgroundResource(R.drawable.bg_no_camera)
            binding.surfaceView.gone()
            CameraWrapper.justPausePreview()
        }
    }

    fun controlBtnClick() {

        when (mainViewModel.recStatus) {
            RecordState.PAUSE -> {
                mainViewModel.resumeRecord()
                binding.waveView.resumeAnim()
            }

            RecordState.REC -> {
                mainViewModel.pauseRecord()
                binding.waveView.pauseAnim()
            }

            RecordState.STOP -> {
                startOrStopRecord()
            }

            else -> {

            }
        }

    }


    /**
     * 开始录制
     */
    private fun startOrStopRecord() {
        logTagD(TAG, "MainActivity点击了startRecord() 当前=${mainViewModel.recStatus}")
        if (mainViewModel.recStatus == RecordState.STOP) {


            startRecordLaunch = launch {
                if (shareByomModel.isEshareByomRunning()) {
                    showByomForceStopDialog(ByomForceStopType.startOrStop)
                    return@launch
                } else {
                    if (isCameraAndeAudioRecourseOccupy()) {
                        val processNames = mainViewModel.getUseMicProcessName()
                        showForceStopDialog(R.string.dialog_mic_occupy_hint, processNames) {
                            if (it) {
                                startOrStopRecord()
                            }
                        }
                        return@launch
                    }
                }

                //当前录制模式 提供语音处理使用
                MeetingHandler.localRecordMode = currentRecordMode.name

                if (currentRecordMode == CAMERA_MODE) {
                    mainViewModel
                        .checkSpace()
                        .yes {
                            launch {
                                mainViewModel.playControlBtnClickable.emit(false)
                                mainViewModel.takePictureAndSave(
                                    binding.photoFlashingLayout,
                                    binding.timeWaterMarkCb.isChecked
                                ) {
                                    lifecycleScope.launch {
                                        mainViewModel.playControlBtnClickable.emit(true)
                                    }
                                }
                            }
                        }.otherwise {
                            launch {
                                mainViewModel.playControlBtnClickable.emit(true)
                            }
                            logTagD(TAG, "存储空间不足")
                            toast(R.string.toast_storage_not_enough_start_camera)
                        }
                    return@launch
                }

                val afChangeListener: AudioManager.OnAudioFocusChangeListener =
                    AudioManager.OnAudioFocusChangeListener {
                        logTagI(TAG, "音频焦点改变${it} - ${audioUtil.getAudioFocusName(it)}")
                    }

                launch {
                    mainViewModel
                        .checkSpace()
                        .yes {
                            val requestAudioFocus =
                                audioUtil.requestAudioFocus(
                                    streamType = AudioManager.STREAM_VOICE_CALL,
                                    focusListener = afChangeListener
                                )
                            if (requestAudioFocus == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                                logTagD(TAG, "成功获取音频焦点")
                                MeetingHandler.localMeetingRecording = true
                                MeetingHandler.localMeetingVideoRecording = true
                                screenFloatx = -1//
                                screenFloaty = -1
                                launch(Dispatchers.IO) {
                                    delay(4000)
                                    MeetingHandler.localMeetingVideoRecording =
                                        currentRecordMode == VIDEO_MODE
                                                && mainViewModel.recStatus == RecordState.REC
                                }

                                // 在开始录制前重新设置混合器启动监听器
                                setupMixerStartListener()
                                mainViewModel.startRecord(::showRecNoticeUI)
                            } else {
                                logTagD(TAG, "获取音频焦点失败")
                                audioUtil.abandonAudioFocus()
                            }

                        }.otherwise {
                            launch {
                                mainViewModel.playControlBtnClickable.emit(true)
                            }
                            logTagD(TAG, "存储空间不足")
                            toast(R.string.toast_storage_not_enough_start_record)
                        }
                }
            }
        } else {
            stopRecord()
        }
    }

    private fun stopRecordAndFinish() {
        isStopping = true
        //停止录制,退出应用
        launch {
            stopRecord()
            finish()
        }
    }

    /**
     * 停止录制
     */
    private fun stopRecord(
        finishDelayTime: Long = 300,
        needShowRenameDialogActivity: Boolean = true
    ) {
        setBiCubicState(1)
        logI(TAG, "stopRecord--finishDelayTime--$finishDelayTime")
        val needToast = mainViewModel.recStatus != RecordState.STOP
        launch {//开始与停止按钮可点击事件之间留一些时间,用于摄像头音频的初始化与释放资源
            mainViewModel.playControlBtnClickable.emit(false)
            delay(1000)
            mainViewModel.playControlBtnClickable.emit(true)
        }
        startRecordLaunch?.cancel()
        delayMoveToBackLaunch?.cancel()
        stopFiveMinutesLaunch?.cancel()
        runOnUiThread {
            binding.screenRecordingLayout.root.gone()
        }
        val savedFilePath = mainViewModel.stopRecord()
        audioUtil.abandonAudioFocus()
        MeetingHandler.localMeetingRecording = false
        MeetingHandler.localMeetingVideoRecording = false
        MeetingHandler.localRecordMode = "null"

        val savedFile = File(savedFilePath)
        val savedFileName = savedFile.name.substringBeforeLast(".")
        val savedFileExt = savedFile.name.substringAfterLast(".")//文件后缀名
        val savedFileDocumentPath = savedFile.absolutePath.substringBeforeLast(savedFile.name)

        if (savedFilePath.isEmpty()) {
            logTagD(TAG, "录制文件保存失败")
        } else if (savedFilePath == "-1") {
            logTagD(TAG, "录制文件保存失败-1")
        } else if (needShowRenameDialogActivity) {
            renameDialogShowing = true
            renameDialogShowed = true
            //在RenameDialogActivity中处理页面更改名称后,页面是否关闭
            val intent = Intent(this, RenameDialogActivity::class.java)
            intent.putExtra("savedFileDocumentPath", savedFileDocumentPath)
            intent.putExtra("savedFilePath", savedFilePath)
            intent.putExtra("savedFileName", savedFileName)
            intent.putExtra("savedFileExt", savedFileExt)
            intent.putExtra("needToast", needToast)
            intent.putExtra("finishDelayTime", finishDelayTime)
            // 添加了这个Flag的Activity,每个都会独立显示在最近任务里,但是关闭后,不会在最近任务里保留残影
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            application.startActivity(intent)
        }

        launch {
            if (mainViewModel.isInFront.not()) {
                logTagV(TAG, "send finishAppFlow :$finishDelayTime")
                App.instance.finishAppFlow.emit(finishDelayTime)
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility", "UnspecifiedRegisterReceiverFlag")
    @OptIn(InternalCoroutinesApi::class)
    override fun initData(savedInstanceState: Bundle?) {
        val filter = IntentFilter().apply {
            addAction(RENAME_DIALOG_SHOWED_ACTION)
        }
        registerReceiver(renameDialogShowedReceiver, filter)
        val filter1 = IntentFilter().apply {
            addAction(START_MUXER_ACTION)
        }

        addMainBroadcastReceiver(
            this,
            onSwitchLanguage = ::closedLocalMeetingRecord,
            onPowerOff = ::doOnPowerOff
        )

        mainViewModel.recStatusLive.observeForever {
            CameraWrapper.recStatus = it
            MuxerManager.instance.setRecStatus(it)
            binding.updateUI(it)
        }

        mainViewModel.prepareTextLive.observe(this) {
            when (it) {
                "3" -> {
                    binding.prepareTimeBgIv.setImageResource(R.drawable.circle_grey_transparent)
                    binding.prepareTimeIv.setImageResource(R.drawable.time3)
                }

                "2" -> {
                    binding.prepareTimeIv.setImageResource(R.drawable.time2)
                    binding.prepareTimeBgIv.setImageResource(R.drawable.circle_grey_transparent)
                }

                "1" -> {
                    binding.prepareTimeBgIv.setImageResource(R.drawable.circle_grey_transparent)
                    binding.prepareTimeIv.setImageResource(R.drawable.time1)
                }

                else -> {
                    binding.prepareTimeIv.setImageDrawable(null)
                    binding.prepareTimeBgIv.setImageDrawable(null)
                }
            }
        }

        // 持续时间
        mainViewModel.meetingDurationLive.observeForever {
            when (it) {
                "1" -> {
                    if (currentRecordMode == SCREEN_MODE) {
                        binding.backIv.isEnabled = false
                        binding.backIv.isClickable = false
                        binding.backIv.isFocusable = false
                        launch {//开始与停止按钮可点击事件之间留一些时间,用于摄像头音频的初始化与释放资源
                            mainViewModel.playControlBtnClickable.emit(false)
                            delay(1000)
                            mainViewModel.playControlBtnClickable.emit(true)
                        }
                        delayMoveToBackLaunch = launch {
                            delay(300)
                            backIvClick()
                        }
                        delayMoveToBackLaunch?.invokeOnCompletion(onCancelling = true) {
                            binding.backIv.isEnabled = true
                            binding.backIv.isClickable = true
                            binding.backIv.isFocusable = true
                        }


                    }
                }

                "2" -> {

                }

                "3" -> {
                    launch(Dispatchers.IO) {
                        mainViewModel.takeAPicture()
                    }
                }

                else -> {
                    binding.recordDurationTv.text = it
                }
            }

            if (recordFloatAudioWindowService?.isServiceAlive() == true) {
                recordFloatAudioWindowService?.setRecordDuringTime(it)
            }

            if (recordFloatScreenWindowService?.isServiceAlive() == true) {
                recordFloatScreenWindowService?.setRecordDuringTime(it)
            }

            if (it.length > 1 && mainViewModel.recStatus == RecordState.REC) {
                binding.screenRecordingLayout.screenRecordingDuringTv.text = it
            }
        }

        // 功能栏是否显示
        mainViewModel.hudShowLive.observe(this) {
            if (it) {
                binding.hudGroup.show()
                binding.slideSelectTabView.post {
                    binding.slideSelectTabView.apply {
                        tabList = RecordModel.values().toMutableList()
                        defaultSelectTabIndex = DEFAULT_RECORD_MODE.number

                        launch {
                            selectResultFlow.collect {
                                mainViewModel.currentRecordModeFlow.emit(it)
                            }
                        }
                        launch {
                            touchable.collect {
                                if (binding.slideSelectTabView.getTargetMode() != SCREEN_MODE
                                    && binding.slideSelectTabView.getTargetMode() != CAMERA_MODE
                                    && binding.slideSelectTabView.getTargetMode() != VIDEO_MODE
                                ) {
                                    binding.screenSelectOptionsGroup.gone()
                                    stopService(
                                        Intent(
                                            this@MainActivity,
                                            RecordFloatTimeWindowService::class.java
                                        )
                                    )
                                }
                            }
                        }

                    }
                }

            } else {
                binding.hudGroup.gone()
            }
        }
        launch {
            mainViewModel.currentRecordModeFlow.collect {
                currentRecordMode = it
                setRecordMode(it)
            }
        }
        NoticeHandler.registerSync(MsgType(MsgType.MEET, MsgType.MEETING_CONFLICT), null) {
            logTagD(TAG, "收到会议邀请")
            if (mainViewModel.recStatus != RecordState.STOP) {
                withContext(Dispatchers.Main) {
                    // 这里之所以切换到主线程执行
                    // 是因为 mediaRecorder.release() 等方法 如果放到子线程中, 有可能会导致崩溃
                    // 只有这里的stopRecord是在子线程中执行, 所以直接修改这里, 没有修改mediaRecorder那
                    stopRecord(1500)
                }
            }

            NoticeMsg().apply {
                put(true)
            }
        }

        NoticeHandler.registerSync(MsgType(MsgType.MEET, MsgType.MEETING_REJECT_CALL_PAGE), null) {
            val appStatus = mainViewModel.recStatus != RecordState.STOP // 只有stop的时候发送false 不拦截
            NoticeMsg().apply {
                put(appStatus)
            }
        }

        NoticeHandler.register(MsgType(MsgType.COMMON, MsgType.COMMON_BYOM_REQUEST), this) {
            if (recordFrontService?.byomIsPairing == true) {
                logTagI(TAG, "收到宜享BYOM申请(Activity),但是正在配对中,不予相应")
                return@register
            }
            logTagI(TAG, "收到宜享BYOM申请(Activity)")
            if (mainViewModel.recStatus != RecordState.STOP) {
                logTagI(TAG, "停止录像并退出")
                stopRecordAndFinish()
            } else {
                logTagI(TAG, "退出")
                if (!isShowingByomForceStopDialog) {// 强制关闭的弹窗显示中,不finish
                    finish()
                }
            }
        }


        launch {
            mainViewModel.currentSubMenuFlow.collect {
                while (binding.coverLayout.isVisible) {
                    delay(200)
                }
                when (it) {
                    getString(R.string.voice_command_record) -> binding.slideSelectTabView.defaultSelectTabIndex =
                        AUDIO_MODE.number

                    getString(R.string.voice_command_video) -> binding.slideSelectTabView.defaultSelectTabIndex =
                        VIDEO_MODE.number

                    getString(R.string.voice_command_screen) -> binding.slideSelectTabView.defaultSelectTabIndex =
                        SCREEN_MODE.number

                    getString(R.string.voice_command_photo) -> binding.slideSelectTabView.defaultSelectTabIndex =
                        CAMERA_MODE.number
                }
            }
        }
        launch {
            mainViewModel.currentViewNavigateFlow.collect {
                logTagD(TAG, "=======currentViewNavigateFlow=====$it")
                mainViewModel.onNavigateReset()
                while (binding.coverLayout.isVisible || targetRecordMode != currentRecordMode) {
                    delay(500)
                }
                delay(ONE_SECOND) //按钮显示一秒
                if (mainViewModel.recStatus == RecordState.STOP) {
                    binding.startOrStopIv.performClick()
                }
            }
        }

        handleIntent(intent)
    }


    /**
     * 显示录制过程中的UI
     */
    private fun showRecNoticeUI(error: MainViewModel.RecError) {
        when (error) {
            is MainViewModel.NoEnoughSpaceError -> {
                if (stopFiveMinutesLaunch?.isActive == true) {
                    return
                }

                stopFiveMinutesLaunch = launch {
                    toast(getString(R.string.toast_rec_storage_not_enough))
                    // 等待5分钟后, 自动停止录制
                    delay(TIME_NO_SPACE_REC)
                    stopRecord()
                }
            }

            is MainViewModel.NoSpaceError -> {
                launch {
                    toast(getString(R.string.toast_rec_storage_stop_recording))
                    delay(1500)
                    stopRecord()
                }
            }

            is MainViewModel.NoTimeError -> {
                val lastTime = error.timeInMin
                if (mainViewModel.recStatus == RecordState.REC) {
                    if (lastTime == 0) {
                        logTagI(TAG, "到时间,停止录像")
                        stopRecord()
                    } else {
                        logTagI(TAG, "toast提示还剩:${lastTime}分钟")
                        toast(getString(R.string.toast_rec_time_not_enough, lastTime.toString()))
                    }
                }

            }

            is MainViewModel.CodecError -> {
                launch {
                    stopRecord(needShowRenameDialogActivity = false)
                    delay(300)
                    SingleBtnCommonFloat(content = getString(R.string.toast_record_error)) { commonFloat ->
                        launch {
                            UserHandler.clearKickOut()
                        }
                        commonFloat.dismiss()
                    }.show()
                }
            }

            else -> {}
        }
    }

    /**
     * 更新各个UI的可见性
     */
    @SuppressLint("ClickableViewAccessibility")
    private fun ActivityMainBinding.updateUI(recordState: RecordState) {

        when (recordState) {
            RecordState.STOP -> { // 录制停止
                prepareTimeIv.gone()
                prepareTimeBgIv.gone()
                recordDurationTv.gone()
                startOrStopIv.setImageResource(R.drawable.ic_start_record)
                startOrStopIv.recording = false
                when (currentRecordMode) {
                    AUDIO_MODE -> {
                        waveView.pauseAnim()
                        waveView.cleanData()
                        timeWaterMarkGroup.gone()
                        flipTheLensGroup.gone()
                        waveView.gone()
                    }

                    SCREEN_MODE -> {
                        screenSelectOptionsGroup.show()
                        timeWaterMarkGroup.show()
                        // 开启摄像头才会显示镜像反转
                        flipTheLensGroup.gone(!mainViewModel.enableWithVideoMark)
                    }

                    CAMERA_MODE -> {
                        timeWaterMarkGroup.show()
                        flipTheLensGroup.show()
                    }

                    else -> {
                        timeWaterMarkGroup.show()
                        flipTheLensGroup.show()
                    }

                }
                backIv.setImageResource(R.drawable.ic_local_back)
                narrowRemindTv.gone()
                pauseOrResumeIv.gone()
                timeWaterMarkCb.isEnabled = true
                selectModelGroup.show()
            }

            RecordState.PREPARE -> {// 录制准备中(倒数3秒)

                prepareTimeIv.show()
                prepareTimeBgIv.show()
                recordDurationTv.gone()
                startOrStopIv.setImageResource(R.drawable.ic_recording)
                startOrStopIv.recording = false
                if (currentRecordMode == AUDIO_MODE) {
                    waveView.gone()
                    backIv.setImageResource(R.drawable.ic_narrow_grey)
                    narrowRemindTv.show()
                } else {
                    backIv.setImageResource(R.drawable.ic_local_back)
                    narrowRemindTv.gone()
                }
                timeWaterMarkGroup.gone()
                flipTheLensGroup.gone()
                pauseOrResumeIv.gone()
                timeWaterMarkCb.isEnabled = false
                selectModelGroup.gone()
                screenSelectOptionsGroup.gone()
            }

            RecordState.REC -> {// 录制中
                prepareTimeIv.gone()
                prepareTimeBgIv.gone()
                when (currentRecordMode) {
                    AUDIO_MODE -> {
                        waveView.show()
                        waveView.resumeAnim()
                        pauseOrResumeIv.show(R.drawable.ic_pause)
                        launch {//开始与停止按钮可点击事件之间留一些时间,用于摄像头音频的初始化与释放资源
                            mainViewModel.playControlBtnClickable.emit(false)
                            delay(1000)
                            mainViewModel.playControlBtnClickable.emit(true)
                        }
                    }

                    VIDEO_MODE -> {
                        pauseOrResumeIv.gone()
                    }

                    SCREEN_MODE -> {
                        if (!mainViewModel.enableWithVideoMark) {
                            CameraWrapper.justReleaseCamera()
                        }
                        binding.screenRecordingLayout.root.show()
                        binding.screenRecordingLayout.root.setOnTouchListener { v, event ->
                            true
                        }
                    }

                    else -> {
                        pauseOrResumeIv.show(R.drawable.ic_pause)
                    }
                }
                recordDurationTv.show()
                startOrStopIv.setImageResource(R.drawable.ic_recording)
                startOrStopIv.recording = true
                timeWaterMarkGroup.gone()
                flipTheLensGroup.gone()

                screenRecordingLayout.controlRecordingIv.show(R.drawable.pause_screen_recording)
                screenRecordingLayout.controlRecordingTv.text =
                    resources.getString(R.string.str_pause_record_screen)
                timeWaterMarkCb.isEnabled = false



                selectModelGroup.gone()
                screenSelectOptionsGroup.gone()
            }

            RecordState.PAUSE -> {// 暂停
                prepareTimeIv.gone()
                prepareTimeBgIv.gone()
                when (currentRecordMode) {
                    AUDIO_MODE -> {
                        waveView.show()
                        waveView.pauseAnim()
                        pauseOrResumeIv.show(R.drawable.ic_resume)
                    }

                    VIDEO_MODE -> {
                        pauseOrResumeIv.gone()
                    }

                    else -> {
                        pauseOrResumeIv.show(R.drawable.ic_resume)
                    }
                }
                recordDurationTv.show()
                startOrStopIv.setImageResource(R.drawable.ic_recording)
                startOrStopIv.recording = false
                timeWaterMarkGroup.gone()

                screenRecordingLayout.controlRecordingIv.show(R.drawable.resume_screen_recording)
                screenRecordingLayout.controlRecordingTv.text =
                    resources.getString(R.string.str_resume_record_screen)
                timeWaterMarkCb.isEnabled = false

                selectModelGroup.gone()
                screenSelectOptionsGroup.gone()
            }
        }
        mainViewModel.changeEShareState()
        if (recordFloatAudioWindowService?.isServiceAlive() == true) {
            recordFloatAudioWindowService?.changeRecordState(recordState.name)
        }
        if (recordFloatScreenWindowService?.isServiceAlive() == true) {
            recordFloatScreenWindowService?.changeRecordState(recordState.name)
        }
    }

    private fun setRecordMode(mode: RecordModel) {
        logTagD(TAG, "切换模式:${mode.recordModeName}")
        when (mode) {
            SCREEN_MODE -> {
                binding.background.setBackgroundColor(resources.getColor(R.color.black))

                if (!isFirstIn) {//第一次进入的时候已经初始化了,增加判断防止第一次进入2次初始化,浪费资源
                    launch {
                        mainViewModel.playControlBtnClickable.emit(false)
                    }

                    initCamera(
                        binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                        Config.SRC_VIDEO_HEIGHT_1080
                    )

                }
                isFirstIn = false
                binding.surfaceView.show()
                binding.waveView.gone()
                binding.timeWaterMarkGroup.show()
                binding.flipTheLensGroup.show()
                binding.screenSelectOptionsGroup.show()
                changeRecordScreenBg(mainViewModel.enableWithVideoMark)
            }

            AUDIO_MODE -> {
                binding.background.setBackgroundResource(R.drawable.bg_no_camera)
                if (mainViewModel.recStatus == RecordState.STOP
                    || mainViewModel.recStatus == RecordState.PREPARE
                ) {
                    binding.waveView.gone()
                } else {
                    binding.waveView.show()
                }
                binding.surfaceView.gone()
                binding.timeWaterMarkGroup.gone()
                binding.flipTheLensGroup.gone()
                binding.screenSelectOptionsGroup.gone()

                launch(Dispatchers.IO) {
                    doWithoutCatch {
                        needInitializeCameraWhenResume = true
                        CameraWrapper.justPausePreview()
                        mainViewModel.releaseCamera()
                    }
                }
            }

            VIDEO_MODE -> {
                binding.background.setBackgroundColor(resources.getColor(R.color.black))

                launch {
                    mainViewModel.playControlBtnClickable.emit(false)
                }

                initCamera(
                    binding.surfaceView, Config.SRC_VIDEO_WIDTH_720,
                    Config.SRC_VIDEO_HEIGHT_720
                )

                binding.surfaceView.show()
                binding.waveView.gone()
                binding.screenSelectOptionsGroup.gone()
                binding.timeWaterMarkGroup.show()
                binding.flipTheLensGroup.show()
            }

            CAMERA_MODE -> {
                launch {
                    mainViewModel.playControlBtnClickable.emit(false)
                }
                initCamera(
                    binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                    Config.SRC_VIDEO_HEIGHT_1080
                )


                binding.waveView.gone()
                binding.surfaceView.show()
                binding.timeWaterMarkGroup.show()
                binding.flipTheLensGroup.show()
                binding.screenSelectOptionsGroup.gone()

            }

            else -> {}
        }

        whetherShowRecordScreenCurrentTimeFloat()

        targetRecordMode = mode
    }

    private fun whetherShowRecordScreenCurrentTimeFloat() {

        if (binding.timeWaterMarkCb.isChecked
            && (currentRecordMode == SCREEN_MODE
                    || currentRecordMode == VIDEO_MODE
                    || currentRecordMode == CAMERA_MODE)
        ) {
            val recordIntent = Intent(this, RecordFloatTimeWindowService::class.java)
            startService(recordIntent)
        } else {
            stopService(Intent(this, RecordFloatTimeWindowService::class.java))
        }
    }

    private fun showNarrowWindow() {
        if (recordFloatScreenWindowService?.isServiceAlive() == true) {
            unbindService(recordScreenConnection)
        }
        if (recordFloatAudioWindowService?.isServiceAlive() == true) {
            unbindService(recordAudioConnection)
        }
        when (currentRecordMode) {
            AUDIO_MODE -> {
                val recordIntent = Intent(this, RecordAudioFloatWindowService::class.java)
                recordIntent.putExtra("offsetX", audioFloatx)
                recordIntent.putExtra("offsetY", audioFloaty)
                recordIntent.putExtra(
                    "recordTime",
                    mainViewModel.meetingDurationLive.value.toString()
                )
                recordIntent.putExtra("recordState", mainViewModel.recStatusLive.value?.name)
                bindService(recordIntent, recordAudioConnection, BIND_AUTO_CREATE)
            }

            SCREEN_MODE -> {
                launch {
//                    if (mainViewModel.enableWithVideoMark) {
//                        val recordImageIntent =
//                            Intent(this@MainActivity, FloatImageWindowService::class.java)
//                        recordImageIntent.putExtra("offsetX", screenFloatx)
//                        recordImageIntent.putExtra("offsetY", screenFloaty)
//                        recordImageIntent.putExtra("imageByte", mainViewModel.saveScreenShot)
//                        bindService(
//                            recordImageIntent,
//                            imageFloatServiceConnection,
//                            BIND_AUTO_CREATE
//                        )
//                        delay(300)
//                        if (resumeInitCameraLaunch?.isActive == true || mainViewModel.initializeCameraJob?.isActive == true) {
//                            resumeInitCameraLaunch?.cancel()
//                            mainViewModel.initializeCameraJob?.cancel()
//                            delay(500)
//                        }
//                    }

                    val recordIntent =
                        Intent(this@MainActivity, RecordScreenFloatWindowService::class.java)
                    recordIntent.putExtra("offsetX", screenFloatx)
                    recordIntent.putExtra("offsetY", screenFloaty)
                    recordIntent.putExtra("recordState", mainViewModel.recStatusLive.value?.name)
                    recordIntent.putExtra("enableVideo", mainViewModel.enableWithVideoMark)
                    recordIntent.putExtra("flipTheLensCbStatus", binding.flipTheLensCb.isChecked)
                    recordIntent.putExtra(
                        "recordTime",
                        mainViewModel.meetingDurationLive.value.toString()
                    )
                    bindService(recordIntent, recordScreenConnection, BIND_AUTO_CREATE)
                }

            }

            else -> {}
        }
    }

    private fun startRecordScreen() {
        mainViewModel.startScreenRecord()
    }


    private fun unbindAllFrontService() {
        doWithoutCatch {
            stopService(Intent(this, RecordFloatTimeWindowService::class.java))
        }
        doWithoutCatch {
            unbindService(recordScreenConnection)
        }

        doWithoutCatch {
            unbindService(recordAudioConnection)
        }

        doWithoutCatch {
            unbindService(recordFrontServiceConnection)
        }

        doWithoutCatch {
            unbindService(imageFloatServiceConnection)
        }
    }

    private val recordScreenConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {

            val binder = service as RecordScreenFloatWindowService.RecordBinder
            recordFloatScreenWindowService = binder.recordService

            //开始录制
            recordFloatScreenWindowService!!.floatWindowScreenServiceListener = object :
                RecordScreenFloatWindowService.FloatWindowScreenServiceListener {
                override fun floatControlBtnClick() {
                    controlBtnClick()
                }

                override fun floatStopRecord() {
                    launch {
                        stopRecord()
                    }
                }

                override fun floatFullScreen() {

                }

                override fun displayFrameCallback() {
                    if (mainViewModel.recStatus == RecordState.PREPARE) {
//                        mainViewModel.resetDuringTime()
                        launch(Dispatchers.IO) {
                            mainViewModel.startScreenRecordWhenCameraIsReady()
                        }
                    }
                    doWithoutCatch {
                        unbindService(imageFloatServiceConnection)
                    }
                }

            }
            // 同步最新的状态
            recordFloatScreenWindowService?.changeRecordState(mainViewModel.recStatus.name)
        }

        override fun onServiceDisconnected(arg0: ComponentName) {}
    }


    private val recordAudioConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {

            val binder = service as RecordAudioFloatWindowService.RecordBinder
            recordFloatAudioWindowService = binder.recordService
            //开始录制
            recordFloatAudioWindowService!!.floatWindowAudioServiceListener = object :
                RecordAudioFloatWindowService.FloatWindowAudioServiceListener {
                override fun floatControlBtnClick() {
                    controlBtnClick()
                }

                override fun floatStopRecord() {
                    launch {
                        stopRecord()
                    }
                }

                override fun floatFullScreen() {

                }

            }
        }

        override fun onServiceDisconnected(arg0: ComponentName) {}
    }

    // 主要处理应用在切换前台和切换后台的一些业务
    private val recordFrontServiceConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {

            val binder = service as RecordingFrontService.RecordBinder
            recordFrontService = binder.recordService

            recordFrontService!!.mAppVisibleChangedListener = object :
                RecordingFrontService.AppVisibleChangedListener {
                override fun onAppVisibleChanged(vis: Boolean) {
                    logTagD(TAG, "onAppVisibleChanged=$vis")
                    mainViewModel.isInFront = vis

                    if (vis) {
                        var hasShowTimeFloat = false
                        val am = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                        val info: List<ActivityManager.RunningServiceInfo> =
                            am.getRunningServices(Int.MAX_VALUE)
                        if (recordFloatScreenWindowService?.isServiceAlive() == true) {
                            screenFloatx = recordFloatScreenWindowService?.getFloatX() ?: 0
                            screenFloaty = recordFloatScreenWindowService?.getFloatY() ?: 0
                            unbindService(recordScreenConnection)
                            doWithoutCatch {
                                unbindService(imageFloatServiceConnection)
                            }
                            needInitializeCameraWhenResume = true

                            if (mainViewModel.recStatus == RecordState.PREPARE) {
                                launch {
                                    mainViewModel.startScreenRecordWhenCameraIsReady()
                                    mainViewModel.resetDuringTime()
                                }
                            }
                        }

                        if (recordFloatAudioWindowService?.isServiceAlive() == true) {
                            audioFloatx = recordFloatAudioWindowService?.getFloatX() ?: 0
                            audioFloaty = recordFloatAudioWindowService?.getFloatY() ?: 0
                            unbindService(recordAudioConnection)
                        }


                        for (runningServiceInfo in info) {
                            val name = runningServiceInfo.service.className
                            if (name.contains(RecordFloatTimeWindowService::class.java.name)) {
                                hasShowTimeFloat = true
                                break
                            }
                        }
                        if (!hasShowTimeFloat) {
                            whetherShowRecordScreenCurrentTimeFloat()
                        }
                    } else {//录像和拍照,不需要展示小窗

                        logTagD(TAG, "mainViewModel.recStatus=${mainViewModel.recStatus}")
                        if (mainViewModel.recStatus == RecordState.REC
                            || mainViewModel.recStatus == RecordState.PREPARE
                            || mainViewModel.recStatus == RecordState.PAUSE
                        ) {
                            when (mainViewModel.currentRecordModeFlow.value) {
                                CAMERA_MODE -> {

                                }

                                VIDEO_MODE -> {
                                    // 录像在后台,停止录像
                                    stopRecordAndFinish()
                                }

                                else -> {
                                    showNarrowWindow()
                                    needInitializeCameraWhenResume = true
                                }
                            }

                        } else {
                            stopService(
                                Intent(
                                    this@MainActivity,
                                    RecordFloatTimeWindowService::class.java
                                )
                            )
                        }
                    }
                }

            }
        }

        override fun onServiceDisconnected(arg0: ComponentName) {}
    }


    private val imageFloatServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            var binder = service as FloatImageWindowService.MyBinder
            binder.setListener(object : FloatImageWindowService.ImageSetListener {
                override fun imageSetFinishedListener() {
                    logTagV(TAG, "imageFloatServiceConnection-callback")
                    launch {
                        delay(500)//防止连接速度快,图片设置速度慢,会出现返回launcher的一瞬间
                        delayMoveToBackLaunch?.cancel()
                        moveTaskToBack(true)
                    }

                }
            })
            launch {
                logTagV(TAG, "imageFloatServiceConnection-while")
                while (!binder.getImageSetStatus()) {
                    delay(100)
                }
                launch(Dispatchers.Main) {
                    binding.prepareTimeBgIv.gone()
                    binding.prepareTimeIv.gone()
                    binding.screenRecordingLayout.root.show()
                }
                delay(100)
                delayMoveToBackLaunch?.cancel()
                moveTaskToBack(true)
                logTagV(TAG, "imageFloatServiceConnection-while-over")
            }


        }

        override fun onServiceDisconnected(name: ComponentName?) {

        }
    }


    /**
     * 展示强制结束App的对话框
     */
    private fun showForceStopDialog(
        msgResourceId: Int,
        processNames: MutableList<ProcessName>, callback: ((Boolean) -> Unit)? = null
    ) {

        if (processNames.isEmpty()) return
        logTagD(TAG, "展示强制退出列表")

        val delayCheckLaunch = launch {//每5秒检测一次当前的麦克风占用情况, 没有占用的就消失掉弹窗
            while (true) {
                delay(5000)
                val processNamesNow = mainViewModel.getUseMicProcessName()
                if (processNamesNow.isEmpty()) {
                    onDismissListenerFloat?.dismiss()
                    callback?.invoke(true)
                    this.cancel()
                    break
                }
            }

        }

        val appNameList = getAppNamesByPkges(processNames)
        // 把appNameList 在数量大于1时变成 name,name1 的格式
        val appName = if (appNameList.size > 1) {
            appNameList.joinToString(separator = getString(R.string.comma)) { it }
        } else {
            appNameList.first()
        }
        val hintStr = getString(msgResourceId, appName)
        onDismissListenerFloat?.dismiss()
        onDismissListenerFloat = DoubleBtnCommonFloat(content = hintStr) { commonFloat, position ->
            commonFloat.dismiss()
            when (position) {
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {
                    logTagI(TAG, "showForceStopDialog-选择取消")
                    callback?.invoke(false)
                }

                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                    logTagI(TAG, "showForceStopDialog-选择确定")
                    val processNamesNow = mainViewModel.getUseMicProcessName()

                    processNamesNow.forEach {
                        mainViewModel.forceStopProcess(it.processName)
                    }
                    callback?.invoke(true)
                }
            }
        }.apply {
            setOnDismissListener {
                launch {
                    mainViewModel.playControlBtnClickable.emit(true)
                }
                delayCheckLaunch.cancel()
            }
        }

        onDismissListenerFloat?.show()

    }

    private fun backIvClick(needFinish: Boolean = false) {
        if (!mainViewModel.isInFront && !needFinish) {
            logTagD(TAG, "页面已经不可见了, 忽略backIvClick事件")
            return
        }
        if (mainViewModel.recStatus != RecordState.STOP) {
            logTagI(TAG, "不处于录制停止状态, 返回键特殊处理")
            if (currentRecordMode == VIDEO_MODE) {
                showStopRecordDialog(needFinish)
            } else if (currentRecordMode == SCREEN_MODE && mainViewModel.enableWithVideoMark) {
                launch {
                    mainViewModel.takeAPicture() {
                        val recordImageIntent =
                            Intent(this@MainActivity, FloatImageWindowService::class.java)
                        recordImageIntent.putExtra("offsetX", screenFloatx)
                        recordImageIntent.putExtra("offsetY", screenFloaty)
                        recordImageIntent.putExtra("imageByte", mainViewModel.saveScreenShot)
                        bindService(
                            recordImageIntent,
                            imageFloatServiceConnection,
                            BIND_AUTO_CREATE
                        )
                        // 接下来返回到桌面的逻辑--跳转到imageFloatServiceConnection的图片处理完成回调
                        launch {
                            binding.prepareTimeIv.setImageDrawable(null)
                            binding.prepareTimeBgIv.setImageDrawable(null)
                            binding.screenRecordingLayout.root.show()
                        }
                    }
                }
            } else {
                delayMoveToBackLaunch?.cancel()
                moveTaskToBack(true)
            }
        } else {
            finish()
        }
    }

    /**
     * 询问是否要结束录制的dialog
     */
    private fun showStopRecordDialog(needFinish: Boolean = false) {
        stopRecordDialog = DoubleBtnCommonFloat(
            showMode = FloatShowMode.SINGLE,
            outSideDismiss = true,
            content = getString(R.string.dialog_stop_record_hint)
        )
        { commonFloat, position ->
            commonFloat.dismiss()
            when (position) {
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {

                }

                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                    if (needFinish) {
                        stopRecordAndFinish()
                    } else {
                        stopRecord()
                    }
                }
            }
        }
        stopRecordDialog?.show()
    }

    /**
     * 释放资源
     */
    private fun releaseResourcesAndFinish(needFinish: Boolean = true) {
        MeetingHandler.localMeetingRecording = false
        MeetingHandler.localMeetingVideoRecording = false
        MeetingHandler.localMeetingRecordCanEShare = true
        MeetingHandler.localRecordMode = "null"
        if (needFinish) {
            logTagD(TAG, "直接关闭releaseResourcesOrFinish=true")
            unbindAllFrontService()
            if (!isFinishing) {
                finish()
            }
            exitProcess(0)
        } else {
            logTagD(TAG, "释放资源releaseResourcesOrFinish=false")
            launch(Dispatchers.IO) {
                CameraWrapper.justReleaseCamera()
                audioUtil.abandonAudioFocus()
                mainViewModel.releaseCamera()
            }

        }
    }

    // 生命周期监听
    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        logTagD(TAG, "LocalMeetingRecord 监听到生命周期变化:$event")
        when (event) {
            Lifecycle.Event.ON_START -> {// 应用到达前台

            }

            Lifecycle.Event.ON_RESUME -> {
                // 初始化摄像头
                if (surfaceViewCreated) {

                    if (renameDialogShowed && !needInitializeCameraWhenResume) {//renamedialog 消失时候,不进行初始化
                        renameDialogShowed = false
                        return
                    } else {
                        renameDialogShowed = false
                    }


                    launch {
                        if (shareByomModel.isEshareByomRunning()) {//byom开启
                            showByomForceStopDialog(ByomForceStopType.initCameraInResume)
                        } else if (isCameraAndeAudioRecourseOccupy()) {
                            val processNames = mainViewModel.getUseMicProcessName()
                            showForceStopDialog(
                                R.string.dialog_mic_occupy_hint_init,
                                processNames
                            ) {
                                if (it) {
                                    initializationCameraInResume()
                                } else {
                                    finish()
                                }
                            }
                        } else if (needInitializeCameraWhenResume) {
                            if (mainViewModel.currentRecordModeFlow.value == RecordModel.SCREEN_MODE && !mainViewModel.enableWithVideoMark) {
                                logTagI(TAG, "录屏,没开启同时录像,不用初始化摄像头")
                                return@launch
                            }
                            initializationCameraInResume()
                        }
                    }
                }
            }

            Lifecycle.Event.ON_PAUSE -> {
                mainViewModel.savePreviewBitmap()
            }

            Lifecycle.Event.ON_STOP -> {
                if (mainViewModel.recStatus == RecordState.STOP) {
                    releaseResourcesAndFinish()
                }
            }

            Lifecycle.Event.ON_DESTROY -> {
                if (mainViewModel.recStatus != RecordState.STOP) {
                    logTagW(TAG, "自动停止录像")
                    stopRecord()
                }
                this.unregisterReceiver(renameDialogShowedReceiver)

                // 移除混合器启动监听器
                MuxerManager.instance.removeOnMixerStartListener()

                releaseResourcesAndFinish()

                PreviewHelper.clear()   // 清除掉预览的bitmap
            }

            else -> {}
        }
    }

    private fun initializationCameraInResume() {
        needInitializeCameraWhenResume = false
        resumeInitCameraLaunch = launch {
            if (currentRecordMode == AUDIO_MODE) {
                return@launch
            } else {
                mainViewModel.playControlBtnClickable.emit(false)
            }
            delay(1000) // 返回主界面,等待一会,再进行初始化,防止相机关闭缓慢,再打开有问题,等问题

            when (currentRecordMode) {
                AUDIO_MODE -> {

                }

                SCREEN_MODE -> {
                    initCamera(
                        binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                        Config.SRC_VIDEO_HEIGHT_1080, needRelease = true
                    )
                }

                VIDEO_MODE ->
                    initCamera(
                        binding.surfaceView, Config.SRC_VIDEO_WIDTH_720,
                        Config.SRC_VIDEO_HEIGHT_720, needRelease = true
                    )

                CAMERA_MODE -> initCamera(
                    binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                    Config.SRC_VIDEO_HEIGHT_1080, needRelease = false
                )
            }

        }
    }

    override fun onUserInteraction() {
        super.onUserInteraction()
        // 用户有操作
        mainViewModel.onUserOperation()
    }

    private fun onScreenOFF() {
        logTagV(TAG, "屏幕关闭")
        mainViewModel.isInFront = false
        launch {
            if (mainViewModel.recStatus != RecordState.STOP) {
                stopRecord()//在后台就结束录制后结束掉进程
            } else {
                doWithoutCatch {
                    needInitializeCameraWhenResume = true
                    CameraWrapper.justPausePreview()
                    CameraWrapper.justReleaseCamera()
                    mainViewModel.releaseCamera()
                }
            }
        }
    }

    // 被杀掉进程的时候,会调用这个方法
    private fun onTaskRemoved() {
        if (mainViewModel.recStatus != RecordState.STOP) {
            logTagW(TAG, "自动停止录像")
            stopRecord()
        }

        releaseResourcesAndFinish()
    }

    override fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KEY_CODE_BTB_SEAT -> {
                if (event?.action == KeyEvent.ACTION_DOWN) {
                    if (mainViewModel.recStatus == RecordState.STOP) {
                        if (getBooleanSystemProp(KEY_TOUCH_PAD_DOCK_WAKE, true)) {
                            releaseResourcesAndFinish()
                        }
                    }
                }
                true
            }

            KeyEvent.KEYCODE_BACK -> {
                if (stopRecordDialog?.isVisible == true) {
                    stopRecordDialog?.dismiss()
                } else {
                    backIvClick()
                }
                true
            }

            KeyEvent.KEYCODE_HOME -> {
                true
            }

            else -> super.onInterceptKeyDown(keyCode, event)
        }
    }

    /**
     * 检查摄像头和麦克风是否被占用
     */
    private fun isCameraAndeAudioRecourseOccupy(): Boolean {
        val processNames = mainViewModel.getUseMicProcessName()
        val name = processNames.firstOrNull {
            it.processName == "com.czur.starry.device.transcription"
        }
        if (name != null) {
            logTagD(TAG, "当前AI字幕占用麦克风,不处理")
            return false
        }
        return processNames.isNotEmpty()
    }

    override fun onRestart() {
        super.onRestart()
        // 从后台回到前台, 直接显示CoverLayout, 如果等initCamera的话, 会有一段时间的延迟
        if (currentRecordMode != AUDIO_MODE) {
            // 如果当前是录音模式, 则不需要显示遮挡布局
            showCoverLayout()
        }
    }

    /**
     * 显示遮挡布局
     * 如果有预览的bitmap, 就显示预览的bitmap
     */
    private fun showCoverLayout() {
        if (currentRecordMode == SCREEN_MODE && !mainViewModel.enableWithVideoMark) {
            logTagD(TAG, "录屏模式, 不启用画中画, 不显示模糊过度画面")
            return
        }
        if (binding.blurCoverIv.isShown) {
            logTagD(TAG, "blurCoverIv 正在显示,return")
            return
        }
        binding.blurCoverIv.setOnClickListener { }

        PreviewHelper.burlPreviewBitmap?.let {
            binding.blurCoverIv.setImageBitmap(it)
            binding.blurCoverIv.show()
        } ?: kotlin.run {
            binding.blurCoverIv.gone()
        }
        binding.coverLayout.alpha = 1F
        binding.coverLayout.show()
        logTagI(TAG, "显示模糊过渡画面")
    }

    /**
     * 隐藏遮挡布局
     */
    private fun goneCoverLayout() {
        if (binding.coverLayout.isGone) return
        binding.coverLayout.let {
            logTagI(TAG, "隐藏遮挡布局")
            it.animate().alpha(0f).setDuration(350).withEndAction {
                it.gone()
            }.start()
        }
    }

    private fun initCamera(
        view: CameraView,
        width: Int,
        height: Int,
        needRelease: Boolean = false,
        initCameraCallback: ((Boolean) -> Unit)? = null,
        needShowCoverLayout: Boolean = true
    ) {
        logStackTrace(TAG)
        logTagI(TAG, "initCamera开始初始化相机")
        if (!surfaceViewCreated) {
            logTagI(TAG, "initCamera开始初始化相机--surfaceViewCreated${surfaceViewCreated}")
            return
        }
        if (needShowCoverLayout) {
            showCoverLayout()
        }

        needInitializeCameraWhenResume = false
        launch {
            mainViewModel.playControlBtnClickable.emit(true)
        }

        logTagI(
            TAG,
            "EshareByomRunning ${shareByomModel.isEshareByomRunning()} ---isCameraAndeAudioRecourseOccupy ${isCameraAndeAudioRecourseOccupy()}"
        )
        if (shareByomModel.isEshareByomRunning()) {//byom开启
            return
        } else if (isCameraAndeAudioRecourseOccupy()) {
            return
        }
        launch {
            mainViewModel.initCamera(
                view, width,
                height, needReleaseFirst = needRelease
            ) {
                logTagI(TAG, "initCamera开始初始化相机 结果:${it}")
                initCameraCallback?.invoke(it)
                if (!it) {
                    runOnUiThread {
                        showErrorCameraDialog()
                    }
                } else {
                    runOnUiThread {
                        goneCoverLayout()
                    }

                    launch {
                        mainViewModel.playControlBtnClickable.emit(true)
                    }
                }
            }
        }
    }

    private fun closedLocalMeetingRecord() {
        if (MeetingHandler.localMeetingRecording || MeetingHandler.localMeetingVideoRecording) {
            runBlocking {
                stopRecordAndFinish()
            }
        }
    }

    private fun doOnPowerOff() {
        logTagI(TAG, "doOnPowerOff")
        if (mainViewModel.recStatus != RecordState.STOP) {
            stopRecord()
        }
    }

    private val renameDialogShowedReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            renameDialogShowing = false
            logTagI(TAG, "renameDialogShowing = false")
        }
    }

    override fun onBackPressed() {
        if (preventBackpress) {
            return
        }
        super.onBackPressed()
    }


    private fun showErrorCameraDialog(info: Int = R.string.toast_init_camera_fail) {
        logTagD(TAG, "showErrorCameraDialog ${info}")
        runOnUiThread {
            preventBackpress = true
            NormDialog.Builder()
                .setTitle(getString(R.string.str_alert_dialog_title))
                .setInfo(getString(info))
                .setConfirmText(getString(R.string.str_dialog_sure))
                .setCancelText(getString(R.string.str_dialog_cancel))
                .setButtonStyle(NormDialog.ButtonStyle.SINGLE_BUTTON)
                .setCancelable(false)
                .setConfirmClickListener {
                    releaseResourcesAndFinish()
                }
                .build().show()
        }
    }


    /**
     * 展示强制结束USB的对话框
     */
    private fun showUSBForceStopDialog() {
        logTagI(TAG, "展示关闭USB外设模式的弹框")
        DoubleBtnCommonFloat(
            content = getString(R.string.dialog_byom_camera_mic_occupy_hint)
        ) { commonFloat, position ->
            commonFloat.dismiss()
            when (position) {
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {
                    finish()
                }

                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                    launch {
                        withLoading {
                            mainViewModel.disableGadget()
                            initCamera(
                                binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                                Config.SRC_VIDEO_HEIGHT_1080, needRelease = true
                            )
                        }
                    }
                }
            }
        }.show()
    }


    /**
     * 展示强制结束byom的对话框
     */
    private fun showByomForceStopDialog(type: ByomForceStopType) {
        logTagI(TAG, "展示强制结束byom的对话框showByomForceStopDialog")
        isShowingByomForceStopDialog = true
        val hintStr =
            getString(R.string.dialog_byom_camera_mic_occupy_hint)
        DoubleBtnCommonFloat(
            content = hintStr,
        ) { commonFloat, position ->
            commonFloat.dismiss()
            when (position) {
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {
                    if (type == ByomForceStopType.initCamera) {
                        finish()
                    } else if (type == ByomForceStopType.initCameraInResume) {
                        finish()
                    } else if (type == ByomForceStopType.startOrStop) {
                        launch {
                            mainViewModel.playControlBtnClickable.emit(true)
                        }
                    }
                }

                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {

                    if (shareByomModel.isEshareByomRunning()) {
                        shareByomModel.setOnEshareRunningStatusChanged(object :
                            EshareByomRunningStatusCallBack {
                            override fun byomRunningCallBack() {
                                launch {
//                                        delay(100)
                                    if (type == ByomForceStopType.initCamera) {
                                        initCamera(
                                            binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                                            Config.SRC_VIDEO_HEIGHT_1080, needRelease = true
                                        )
                                    } else if (type == ByomForceStopType.initCameraInResume) {
                                        initializationCameraInResume()
                                    } else if (type == ByomForceStopType.startOrStop) {
                                        startOrStopRecord()
                                    }
                                }
                            }
                        })
                        shareByomModel.stopByom()


                    } else {
                        if (type == ByomForceStopType.initCamera) {
                            initCamera(
                                binding.surfaceView, Config.SRC_VIDEO_WIDTH_1080,
                                Config.SRC_VIDEO_HEIGHT_1080, needRelease = true
                            )
                        } else if (type == ByomForceStopType.initCameraInResume) {
                            initializationCameraInResume()
                        } else if (type == ByomForceStopType.startOrStop) {
                            startOrStopRecord()
                        }
                    }

                }
            }
        }.apply {
            setOnDismissListener {
                isShowingByomForceStopDialog = false
            }
            show()
        }
    }


}