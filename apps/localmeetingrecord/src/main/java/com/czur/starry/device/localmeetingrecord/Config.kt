package com.czur.starry.device.localmeetingrecord

import android.media.AudioFormat
import android.media.MediaRecorder
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MB_SI
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.localmeetingrecord.monitor.audio.MultiChannelMixer

/**
 * Created by 陈丰尧 on 2022/8/6
 */
object Config {

    const val RENAME_DIALOG_SHOWED_ACTION = "com.localmeetingrecord.renameDialogShowed"
    const val START_MUXER_ACTION = "com.czur.starry.device.localmeetingrecord.startMuxer"
    const val IS_PORT = false   //是否竖屏
    const val SRC_VIDEO_WIDTH_2160 = 3840
    const val SRC_VIDEO_HEIGHT_2160 = 2160

    const val SRC_VIDEO_WIDTH_1080 = 1920
    const val SRC_VIDEO_HEIGHT_1080 = 1080

    const val SRC_VIDEO_WIDTH_720 = 1280
    const val SRC_VIDEO_HEIGHT_720 = 720

    const val SRC_VIDEO_WIDTH_360 = 640
    const val SRC_VIDEO_HEIGHT_360 = 360

    var SRC_VIDEO_WIDTH = SRC_VIDEO_WIDTH_720
    var SRC_VIDEO_HEIGHT = SRC_VIDEO_HEIGHT_720

    var DEFAULT_RECORD_MODE = RecordModel.SCREEN_MODE

    const val SCREEN_FLOAT_WIDTH = 371 // 屏幕录制浮窗默认宽度
    const val SCREEN_FLOAT_HEIGHT_WITH_CONTROL = 216+56 // 高度
    const val SCREEN_FLOAT_HEIGHT_CAMERA = 216 // 高度

    const val SCREEN_FLOAT_X =
        SRC_VIDEO_WIDTH_1080 - SCREEN_FLOAT_WIDTH - 10 // 屏幕录制浮窗默认位置x 10是悬浮窗默认距离边缘的距离
    const val SCREEN_FLOAT_Y = 52 // y

    const val AUDIO_FLOAT_WIDTH = 416 // 音频录制浮窗默认宽度
    const val AUDIO_FLOAT_HEIGHT = 105 // 高度

    const val AUDIO_FLOAT_X =
        SRC_VIDEO_WIDTH_1080 - AUDIO_FLOAT_WIDTH - 10 // 音频录制浮窗默认位置x 10是悬浮窗默认距离边缘的距离
    const val AUDIO_FLOAT_Y = 52 // y


    const val TIME_FLOAT_WIDTH = 350 // 浮窗默认宽度
    const val TIME_FLOAT_HEIGHT = 60 // 高度

    const val TIME_FLOAT_X = SRC_VIDEO_WIDTH_1080 - TIME_FLOAT_WIDTH - 10 // 浮窗默认位置x
    const val TIME_FLOAT_Y = SRC_VIDEO_HEIGHT_1080 - TIME_FLOAT_HEIGHT - 10 // y 10是悬浮窗默认距离边缘的距离

    const val VIDEO_FILE_FOLDER_NAME = "localMeetingVideo"
    const val AUDIO_EXTENSION = ".aac"
    const val VIDEO_EXTENSION = ".mp4"
    const val EXTENSION_TMP = ".tmp"

    // 时间水印默认状态
    const val DEF_ENABLE_TIME_WATERMARK = true

    // 录像开始等待时间
    const val PREPARE_TIME_IN_SECOND = 3

    // 水印位置
    const val TIME_WATER_MARK_OFFSET_X = 990 // 按照1280算的
    const val TIME_WATER_MARK_OFFSET_Y = 720 - 16 - 25   // 按照720算的 水印位置在右下角, 边距25, 文字高度16
    const val TIME_WATER_MARK_PATTERN = "yyyy-MM-dd HH:mm:ss"
    const val TIME_WATER_MARK_PATTERN_EN = "MM-dd-yyyy HH:mm:ss"  // 英文版水印格式

    // 用户没有操作时, 隐藏功能栏的时间
    const val TIME_HIDE_DELAY = 3 * ONE_SECOND

    // 检测到容量不足后, 还能录制多久
    const val TIME_NO_SPACE_REC = 5 * ONE_MIN

    // 存储空间大于500MB才能进行录制
    const val SIZE_ENOUGH_REC = 500 * ONE_MB_SI

    // 音频录制源
//    const val AUDIO_SOURCE = MediaRecorder.AudioSource.MIC
    const val AUDIO_SOURCE = MediaRecorder.AudioSource.MIC
    const val AUDIO_SOURCE_SUBMIX = MediaRecorder.AudioSource.REMOTE_SUBMIX

    // 音频采样率
    const val AUDIO_SAMPLE_RATE = 16000 // 设备平台只支持 16000的采样率
    const val AUDIO_CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
    const val AUDIO_ENCODING_BIT_RATE = 96000   // 设置编码器的码率

    // 视频编码码率配置 (bps)
    const val VIDEO_BIT_RATE_4K = 20_000_000    // 4K: 20 Mbps
    const val VIDEO_BIT_RATE_1080P = 6_000_000  // 1080p: 6 Mbps
    const val VIDEO_BIT_RATE_720P = 3_500_000   // 720p: 3.5 Mbps
    const val VIDEO_BIT_RATE_360P = 1_500_000   // 360p: 1.5 Mbps

    // 视频编码质量模式
    enum class VideoQuality(val bitRateMultiplier: Float) {
        LOW(0.7f),      // 低质量：码率 * 0.7
        NORMAL(1.0f),   // 标准质量：码率 * 1.0
        HIGH(1.5f)      // 高质量：码率 * 1.5
    }

    // 默认视频质量
    var DEFAULT_VIDEO_QUALITY = VideoQuality.NORMAL

    val AUDIO_MIX_TYPE = MultiChannelMixer  // 混音类型: 左右声道各一个音源

    // 16bit
    const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT

    // 控制栏是否自动隐藏
    const val CONTROL_BAR_AUTO_HIDE = false

    // 录像的最短时间
    const val RECORD_MIN_DURATION = ONE_SECOND

    // 录像的最长时间
    const val RECORD_MAX_DURATION = 5 * ONE_HOUR

    // 录像开始前多长时间,暂停按钮禁止使用
    const val PAUSE_DISABLE_TIME = ONE_SECOND

    // 拍照文件的扩展名
    const val PHOTO_FILE_EXTENSION = "jpg"
    const val PHOTO_FILE_FOLDER_NAME = "Pictures/Screenshots"

    const val PHOTO_FILE_WATERMARK_FORMAT = "yyyy/MM/dd HH:mm:ss"

    const val OPEN_CAMERA_RETRY_TIMES = 6
}