package com.czur.starry.device.localmeetingrecord

import android.media.MediaFormat
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.czur.starry.device.localmeetingrecord.mdoel.RecordConfigModel
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * Android 14视频编码优化测试
 * 测试不同Android版本下的编码参数设置
 */
@RunWith(AndroidJUnit4::class)
class VideoEncodingTest {

    @Test
    fun testFrameRateOptimization() {
        // 模拟Android 14的帧率优化
        val isAndroid14OrAbove = Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
        val frameRate = if (isAndroid14OrAbove) 20 else 15
        
        if (isAndroid14OrAbove) {
            assertEquals("Android 14+应该使用20fps", 20, frameRate)
        } else {
            assertEquals("Android 13及以下应该使用15fps", 15, frameRate)
        }
        
        println("当前Android版本: ${Build.VERSION.SDK_INT}, 使用帧率: ${frameRate}fps")
    }
    
    @Test
    fun testDynamicBitRateCalculation() {
        // 测试动态码率计算函数
        fun calculateDynamicBitRate(width: Int, height: Int): Int {
            val pixelCount = width * height
            return when {
                pixelCount >= 8_000_000 -> Config.VIDEO_BIT_RATE_4K
                pixelCount >= 2_000_000 -> Config.VIDEO_BIT_RATE_1080P
                pixelCount >= 900_000 -> Config.VIDEO_BIT_RATE_720P
                else -> Config.VIDEO_BIT_RATE_360P
            }
        }
        
        // 测试不同分辨率的码率计算
        assertEquals("4K分辨率应该使用4K码率", 
            Config.VIDEO_BIT_RATE_4K, calculateDynamicBitRate(3840, 2160))
        assertEquals("1080p分辨率应该使用1080p码率", 
            Config.VIDEO_BIT_RATE_1080P, calculateDynamicBitRate(1920, 1080))
        assertEquals("720p分辨率应该使用720p码率", 
            Config.VIDEO_BIT_RATE_720P, calculateDynamicBitRate(1280, 720))
        assertEquals("360p分辨率应该使用360p码率", 
            Config.VIDEO_BIT_RATE_360P, calculateDynamicBitRate(640, 360))
            
        println("动态码率计算测试通过")
    }
    
    @Test
    fun testInputDelayOptimization() {
        // 测试视频输入延迟优化
        val inputDelay = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            33 // Android 14+: 33ms
        } else {
            50 // Android 13及以下: 50ms
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            assertEquals("Android 14+应该使用33ms延迟", 33, inputDelay)
        } else {
            assertEquals("Android 13及以下应该使用50ms延迟", 50, inputDelay)
        }
        
        println("当前Android版本: ${Build.VERSION.SDK_INT}, 使用输入延迟: ${inputDelay}ms")
    }
    
    @Test
    fun testEncodingParametersForAndroid14() {
        val isAndroid14OrAbove = Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
        
        if (isAndroid14OrAbove) {
            // Android 14+应该使用优化参数
            val iFrameInterval = 2
            val bitrateMode = MediaFormat.BITRATE_MODE_VBR
            val quality = 80
            
            assertEquals("Android 14+ I帧间隔应该是2秒", 2, iFrameInterval)
            assertEquals("Android 14+应该使用VBR模式", MediaFormat.BITRATE_MODE_VBR, bitrateMode)
            assertEquals("Android 14+质量等级应该是80", 80, quality)
            
            println("Android 14+优化参数测试通过")
        } else {
            // Android 13及以下使用标准参数
            val iFrameInterval = 1
            assertEquals("Android 13及以下 I帧间隔应该是1秒", 1, iFrameInterval)
            
            println("Android 13及以下标准参数测试通过")
        }
    }
}
