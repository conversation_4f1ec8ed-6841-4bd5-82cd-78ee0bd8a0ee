package com.czur.starry.device.localmeetingrecord

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.czur.starry.device.baselib.utils.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Toast队列问题测试
 * 测试连续多次调用toast是否会产生队列效果
 */
@RunWith(AndroidJUnit4::class)
class ToastTest {

    @Test
    fun testToastQueuePrevention() = runBlocking {
        val context = ApplicationProvider.getApplicationContext<Context>()
        
        // 模拟连续多次调用toast
        repeat(5) { index ->
            context.toast("测试Toast消息 $index")
            delay(100) // 短间隔调用
        }
        
        // 等待一段时间观察是否只显示最后一个Toast
        delay(3000)
        
        // 如果修复成功，应该只看到最后一个Toast，而不是5个Toast排队显示
        println("Toast队列测试完成")
    }
    
    @Test
    fun testToastCancellation() = runBlocking {
        val context = ApplicationProvider.getApplicationContext<Context>()
        
        // 显示第一个Toast
        context.toast("第一个Toast")
        delay(500)
        
        // 立即显示第二个Toast，应该取消第一个
        context.toast("第二个Toast（应该取消第一个）")
        
        delay(2000)
        println("Toast取消测试完成")
    }
}
