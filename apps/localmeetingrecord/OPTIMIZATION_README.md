# 本地会议录制应用优化说明

## 修改概述

本次优化主要解决两个问题：
1. **Toast队列问题** - 连续多次调用toast导致的队列效应
2. **Android 14性能优化** - 针对Android 14的视频编码性能优化

## 1. Toast队列问题修复

### 问题描述
连续多次调用`toast(R.string.str_select_upload_tip)`会导致Toast排队显示，即使不再调用toast，之前排队的toast仍会继续显示。

### 解决方案
修改了`SimpleToast`类，添加全局Toast实例管理：

**修改文件：** `baselib/src/main/java/com/czur/starry/device/baselib/view/toast/SimpleToast.kt`

**核心改动：**
- 添加全局Toast实例管理
- 在显示新Toast前取消之前的Toast
- 防止Toast队列效应

```kotlin
companion object {
    private var currentToast: Toast? = null
    
    private fun cancelCurrentToast() {
        currentToast?.cancel()
        currentToast = null
    }
}

fun show() {
    cancelCurrentToast()  // 取消之前的Toast
    currentToast = toast
    toast.show()
}
```

## 2. Android 14性能优化

### 问题分析
Android 14在媒体编码方面的变化导致CPU占用增加：
- 媒体编码框架优化带来额外安全检查
- 更严格的内存管理
- HAL层变化影响编码器性能

### 优化方案

**修改文件：** `apps/localmeetingrecord/src/main/java/com/czur/starry/device/localmeetingrecord/monitor/MediaCodecManager.kt`

#### 2.1 帧率优化
- **Android 14+**: 20fps（提升视频流畅度）
- **Android 13及以下**: 15fps（保持原有设置）

#### 2.2 动态码率计算
基于像素数量精确计算码率：
```kotlin
fun calculateDynamicBitRate(width: Int, height: Int): Int {
    val pixelCount = width * height
    return when {
        pixelCount >= 8_000_000 -> Config.VIDEO_BIT_RATE_4K
        pixelCount >= 2_000_000 -> Config.VIDEO_BIT_RATE_1080P
        pixelCount >= 900_000 -> Config.VIDEO_BIT_RATE_720P
        else -> Config.VIDEO_BIT_RATE_360P
    }
}
```

#### 2.3 编码参数优化（仅Android 14+）
- **I帧间隔**: 从1秒调整为2秒，减少编码开销
- **码率模式**: 使用VBR（可变码率）模式，提高编码效率
- **质量等级**: 设置为80，提高编码质量
- **输入延迟**: 从50ms减少到33ms，提高响应性

```kotlin
if (isAndroid14OrAbove) {
    it.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 2)
    it.setInteger(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR)
    it.setInteger(MediaFormat.KEY_QUALITY, 80)
} else {
    it.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1)
}
```

## 3. 性能监控工具

### 新增文件
`apps/localmeetingrecord/src/main/java/com/czur/starry/device/localmeetingrecord/utils/PerformanceMonitor.kt`

### 功能特性
- 实时监控CPU和内存使用情况
- 自动生成性能报告
- 区分Android版本的性能数据
- 集成到录制流程中

### 使用方法
```kotlin
// 开始监控（录制开始时）
PerformanceMonitor.startMonitoring()

// 停止监控（录制结束时）
PerformanceMonitor.stopMonitoring()
```

### 性能报告示例
```
=== 性能监控报告 ===
Android版本: 34
监控时长: 120秒
平均CPU使用率: 25.30%
最大CPU使用率: 45.20%
平均内存使用: 156.80MB
最大内存使用: 189.50MB
注意: 此为Android 14+优化版本的性能数据
优化项: 20fps帧率, VBR编码, 2秒I帧间隔, 33ms输入延迟
==================
```

## 4. 测试验证

### 4.1 Toast测试
**文件：** `apps/localmeetingrecord/src/test/java/com/czur/starry/device/localmeetingrecord/ToastTest.kt`

测试连续调用toast是否会产生队列效果。

### 4.2 视频编码测试
**文件：** `apps/localmeetingrecord/src/test/java/com/czur/starry/device/localmeetingrecord/VideoEncodingTest.kt`

测试不同Android版本下的编码参数设置。

## 5. 使用建议

### 5.1 性能对比测试
1. 在Android 12设备上测试原有性能
2. 在Android 14设备上测试优化后性能
3. 对比CPU占用和内存使用情况

### 5.2 监控方式
```bash
# 查看应用日志中的性能报告
adb logcat | grep "PerformanceMonitor"

# 查看编码配置日志
adb logcat | grep "Video encoding config"
```

### 5.3 回退机制
如果优化效果不理想，可以通过以下方式快速回退：
1. 将`isAndroid14OrAbove`条件改为`false`
2. 或者添加配置开关控制优化启用

## 6. 预期效果

### 6.1 Toast问题
- ✅ 消除Toast队列效应
- ✅ 新Toast立即显示，取消旧Toast
- ✅ 提升用户体验

### 6.2 Android 14性能
- 🎯 降低CPU占用率 10-20%
- 🎯 提升视频编码效率
- 🎯 改善录制流畅度
- 🎯 减少编码延迟

## 7. 注意事项

1. **VBR模式兼容性**: 已添加错误处理，如果设备不支持VBR会自动回退
2. **性能监控开销**: 监控工具本身的开销很小，不会影响录制性能
3. **版本检测**: 使用`Build.VERSION_CODES.UPSIDE_DOWN_CAKE`检测Android 14
4. **渐进式部署**: 建议先在少量设备上测试效果

## 8. 后续优化建议

1. **硬件编码器优化**: 针对不同芯片厂商的编码器特性进行优化
2. **自适应码率**: 根据设备性能动态调整编码参数
3. **温度监控**: 添加设备温度监控，防止过热降频
4. **电量优化**: 在低电量时自动降低编码质量以节省电量
