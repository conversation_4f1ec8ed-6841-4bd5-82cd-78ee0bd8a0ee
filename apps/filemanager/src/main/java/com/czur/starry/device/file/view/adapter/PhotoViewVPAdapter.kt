package com.czur.starry.device.file.view.adapter

import android.app.Activity
import android.content.Context
import android.util.Size
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.DownsampleStrategy
import com.bumptech.glide.load.resource.bitmap.Rotate
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.BaseDifferSyncAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.PhotoInfo
import com.czur.starry.device.file.view.vm.PhotoViewModel
import com.github.chrisbanes.photoview.PhotoView

class PhotoViewVPAdapter : BaseDifferSyncAdapter<PhotoInfo>() {

    //    private var photoMap: MutableMap<Int, PhotoView> = mutableMapOf()
    private var photoMap: LimitedMap<Int, PhotoView> = LimitedMap(5)

    private val TAG = "PhotoViewVPAdapter"
    private var context: Context? = null

    private var photoView: PhotoView? = null
    fun setContext(context: Context) {
        this.context = context
    }

    fun scaleUp(targetIndex: Int) {
        photoMap[targetIndex]?.scaleUp()
    }

    fun scaleDown(targetIndex: Int) {
        photoMap[targetIndex]?.scaleDown()
    }

    fun resetPhotoInfo() {
        val copyDataList = getCopyDataList()
        for (i in copyDataList.indices) {
            val photoInfo = copyDataList[i]
            photoInfo.rotateDegree = 0
        }

        val iterator = photoMap.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            entry.value.scale = 1.0f
        }

        setData(copyDataList)
    }

    // 设置图片的旋转角度,其他的都设置为0
    fun setPhotoInfoDegree(targetIndex: Int, currentDegree: Int) {
        val copyDataList = getCopyDataList()
        for (i in copyDataList.indices) {
            if (i == targetIndex) {
                copyDataList[i].rotateDegree = currentDegree
            } else {
                copyDataList[i].rotateDegree = 0
            }
        }
        setData(copyDataList)
    }

    fun getCopyDataList(): List<PhotoInfo> {
        return getDataList().map {
            it.copy()
        }
    }

    fun refreshPhotoWithInfo(viewModel: PhotoViewModel, photoInfo: PhotoInfo) {
        val findFile = viewModel.fileEntities.find {
            it.absPath == photoInfo?.entityPath
        }

        val index = viewModel.fileEntities.indexOf(findFile)

        val listNew = getDataList().map {
            it.copy()
        }
        if (photoInfo.error) {
            listNew[index].error = true
        } else {
            listNew[index].filePath = photoInfo?.filePath!!
        }

        setData(listNew)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        BaseVH(R.layout.item_photo_layout, parent)

    override fun areContentsTheSame(oldItem: PhotoInfo, newItem: PhotoInfo): Boolean {
        return oldItem.filePath == newItem.filePath
                && oldItem.rotateDegree == newItem.rotateDegree
    }

    override fun areItemsTheSame(oldItem: PhotoInfo, newItem: PhotoInfo): Boolean {
        return oldItem.entityPath == newItem.entityPath
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: PhotoInfo) {
        val photoView: PhotoView = holder.getView(R.id.photoView)
        photoMap[position] = photoView
        this.photoView = photoView
        (context as Activity).registerForContextMenu(photoView)
        val maxScale = photoView.maximumScale
        val maxWidth = (getScreenWidth() * maxScale).toInt()
        val maxHeight = (getScreenHeight() * maxScale).toInt()
        val maxSize = Size(maxWidth, maxHeight)

        val targetSize = if (itemData.rotateDegree % 180 == 0) {
            getTargetSize(Size(getScreenWidth(), getScreenHeight()), maxSize)
        } else {
            getTargetSize(Size(getScreenHeight(), getScreenWidth()), maxSize)
        }
        logTagD(TAG, "loadLocalImage:${itemData.filePath}")

        val request = Glide.with(context!!)

        val requestBuilder = if (itemData.error) {
            request.load(R.drawable.photo_error)
        } else if (itemData.filePath.isEmpty()) {
            request.load(R.drawable.photo_placeholder)
        } else {
            request.load(itemData.filePath)
        }
        var format =
            requestBuilder.placeholder(R.drawable.photo_placeholder)
                .error(R.drawable.photo_error)
                .format(DecodeFormat.PREFER_RGB_565)
        if (itemData.rotateDegree % 360 != 0) {
            format = format.transform(Rotate(itemData.rotateDegree % 360))
        }
        format.override(targetSize.width, targetSize.height)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .downsample(DownsampleStrategy.CENTER_INSIDE)
            .into(photoView)
        logTagI(
            TAG,
            "Pic loadSize: ${targetSize.width}x${targetSize.height}, picSize: ${itemData.width}x${itemData.height}, rotateDegree: ${itemData.rotateDegree}"
        )

        photoView.isIgnoreLeft = position == 0
        photoView.isIgnoreRight = position == itemCount - 1
    }

    private fun getTargetSize(picSize: Size, maxSize: Size): Size {

        if (picSize.width <= maxSize.width && picSize.height <= maxSize.height) {
            // 图片尺寸比最大尺寸小
            return picSize
        }

        if (picSize.width > maxSize.width && picSize.height > maxSize.height) {
            // 图片尺寸比最大尺寸大
            return maxSize
        }

        // 图片的1条边比最大尺寸大
        var targetWidth = maxSize.width
        var targetHeight = maxSize.height
        if (picSize.width > maxSize.width) {
            targetHeight = (picSize.height / maxSize.width / picSize.width.toFloat()).toInt()
        } else {
            targetWidth = (picSize.width / maxSize.height / picSize.height.toFloat()).toInt()
        }
        return Size(targetWidth, targetHeight)


    }

    class LimitedMap<K, V>(private val maxSize: Int) :
        LinkedHashMap<K, V>(maxSize + 1, 1.0f, true) {

        override fun removeEldestEntry(eldest: MutableMap.MutableEntry<K, V>) = size > maxSize
    }
}