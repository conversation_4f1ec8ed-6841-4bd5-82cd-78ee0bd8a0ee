package com.czur.starry.device.file.view.dialog

import android.graphics.Bitmap
import android.graphics.Color
import android.os.Bundle
import com.czur.czurutils.img.QrCodeUtil
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.file.databinding.FloatAiTransShareQrCodeBinding
import com.czur.starry.device.file.server.AITransServer
import com.czur.starry.device.sharescreen.esharelib.util.logoBitmap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/12/27
 */
private const val TAG = "AITransShareQRCodeFloat"

class AITransShareQRCodeFloat : CZVBFloatingFragment<FloatAiTransShareQrCodeBinding>() {
    private val transferServer: AITransServer by lazy { HttpManager.getService() }
    private val id by lazy { arguments?.getString("id", "") ?: "" }
    private val onlyAsr by lazy { arguments?.getBoolean("onlyAsr", false) == true }

    override fun FloatAiTransShareQrCodeBinding.initBindingViews() {
        closeBtn.setOnClickListener {
            dismiss()
        }

        loadingErrorBgView.setOnDebounceClickListener {
            refreshQrCode()
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        refreshQrCode()
    }

    private fun refreshQrCode() {
        launch {
            binding.loadingErrorGroup.gone()
            binding.aiTransQrCodeIv.invisible()
            binding.loadingProgressBar.show()

            val bmp = getQrCodeBmp(id, onlyAsr == true).getOrNull()
            binding.loadingProgressBar.gone()
            if (bmp == null) {
                logTagE(TAG, "获取二维码失败")
                binding.loadingErrorGroup.show()
            } else {
                binding.loadingErrorGroup.gone()
                binding.aiTransQrCodeIv.show()
                binding.aiTransQrCodeIv.setImageBitmap(bmp)
            }
        }
    }

    private suspend fun getQrCodeBmp(id: String, onlyAsr: Boolean): Result<Bitmap> {
        var codeUrl = getAiTransQrCodeInfo(id)

        if (codeUrl.isEmpty()) {
            return Result.failure(Exception("获取二维码失败"))
        }

        if (onlyAsr == true) {//如果没有summaryPath,说明只是asr,直接显示asr的分享二维码
            codeUrl += "?asr=true"
        }
        codeUrl += "?genTime=${System.currentTimeMillis()}"
        val bmp = QrCodeUtil.generateQrCodeBmp(codeUrl) {
            this.pointColor = 0xFF5879FC.toInt()
            this.bgColor = Color.WHITE
            this.edge = 400
            this.logoConfig {
                this.logoBmp = logoBitmap
                delPadding = true
            }

        }
        return Result.success(bmp)
    }

    private suspend fun getAiTransQrCodeInfo(id: String?): String {
        return withContext(Dispatchers.IO) {
            var codeUrl = ""
            val aiTransQrCodeInfo = transferServer.postShareQrCode(id!!)
            try {
                if (aiTransQrCodeInfo.isSuccess) {
                    codeUrl = aiTransQrCodeInfo.body
                    codeUrl
                } else {
                    ""
                }
            } catch (e: Exception) {
                logTagE(TAG, "获取二维码失败 ${e.message.toString()}")
                ""
            }
        }
    }
}