package com.czur.starry.device.file.adapter

import android.view.ViewGroup
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseDifferSyncAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.view.FilePadFragment.Companion.FILE_NORMAL_STATUS
import com.czur.starry.device.file.view.FilePadFragment.Companion.FILE_UPLOADING_STATUS
import com.czur.starry.device.file.view.FilePadFragment.Companion.FILE_UPLOAD_INTERRUPT_STATUS
import com.czur.starry.device.file.widget.FileItemView

const val MODE_GRID = 0
const val MODE_LIST = 1

data class FileViewEntity(
    var isSelect: Boolean,       // 是否选中
    var showMode: Int,            // 显示类型
    var isSelectMode: Boolean,     // 是否选择模式
    val fileEntity: FileEntity,
    var isUploadStatus: Int    //0:非上传状态；1：上传中；2：中断文件
) {


    companion object {
        fun createByFileEntity(fileEntity: FileEntity, showMode: Int): FileViewEntity {
            return FileViewEntity(
                false,
                showMode,
                false,
                fileEntity,
                0
            )
        }
    }

}

private val NOT_CREATE_OPEN_MENU_BELONG_LIST =
    listOf(AccessType.LOCAL_MEETING, AccessType.RECORD)
const val TAG = "FilePadAdapter"

class FilePadAdapter : BaseDifferSyncAdapter<FileViewEntity>() {
    val fileEntities: List<FileEntity>
        get() = currentList.map { it.fileEntity }
    private var showMode = MODE_GRID

    /**
     * 通过第三方打开的回调
     */
    var onOpenByOtherAppClickListener: ((fileEntity: FileEntity) -> Unit)? = null

    /**
     * 选中的数量
     */
    val selectCount = DifferentLiveData(0)

    /**
     * 选择上传中断的数量
     */
    var selectUpload = 0

    /**
     * 标记当前是否选择模式
     */
    private var currentSelectedMode = false

    /**
     * 切换显示模式
     */
    fun changeLayoutType(showMode: Int) {
        if (this.showMode == showMode) {
            return
        }
        this.showMode = showMode
        val newData = currentList.map {
            it.copy(showMode = showMode)
        }
        setData(newData)
    }

    /**
     * 更新全部数据,同时会清除选中状态
     */
    fun updateAllData(newData: List<FileEntity>) {
        val showData = newData.map {
            FileViewEntity.createByFileEntity(it, showMode)
        }
        setData(showData)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        BaseVH(R.layout.item_file_pad, parent)


    /**
     * 改变指定Item的Select模式
     */
    fun changeSelect(pos: Int) {
        val currentList: List<FileViewEntity> = currentList
        val newList = currentList.mapIndexed { index, fileViewEntity ->
            if (index == pos) {
                selectUpload =
                    if (fileViewEntity.isUploadStatus == FILE_UPLOAD_INTERRUPT_STATUS) {
                        if (fileViewEntity.isSelect) {
                            selectUpload - 1
                        } else {
                            selectUpload + 1
                        }
                    } else {
                        selectUpload
                    }


                selectCount.value = if (fileViewEntity.isSelect) {
                    (selectCount.value ?: 0) - 1
                } else {
                    (selectCount.value ?: 0) + 1
                }

                fileViewEntity.copy(isSelect = !fileViewEntity.isSelect)
            } else {
                fileViewEntity
            }
        }
        setData(newList)
    }

    /**
     * 改变显示上传状态
     */
    fun changeUploadUI(uploadList: List<String>, pauseList: List<String>) {
        val currentList: List<FileViewEntity> = currentList
        val newList = currentList.map { fileViewEntity ->
            when (fileViewEntity.fileEntity.name) {
                in uploadList -> {
                    if (fileViewEntity.isSelect) {
                        selectCount.value = (selectCount.value ?: 0) - 1
                    }
                    fileViewEntity.copy(isUploadStatus = FILE_UPLOADING_STATUS, isSelect = false)
                }

                in pauseList -> {
                    fileViewEntity.copy(isUploadStatus = FILE_UPLOAD_INTERRUPT_STATUS)
                }

                else -> {
                    fileViewEntity.copy(isUploadStatus = FILE_NORMAL_STATUS)
                }
            }

        }
        setData(newList)
    }

    /**
     * 清除所有选中的Item
     */
    fun clearSelect() {
        val currentList = currentList
        val newList = currentList.map {
            it.copy(isSelect = false, isSelectMode = currentSelectedMode)
        }
        setData(newList)
        selectUpload = 0
        selectCount.value = 0
    }

    /**
     * 设置选择模式
     */
    fun setCurrentMode(isSelectMode: Boolean) {
        currentSelectedMode = isSelectMode
        val newData = currentList.map {
            it.copy(isSelect = false, isSelectMode = currentSelectedMode)
        }
        setData(newData)
    }

    /**
     * 全选
     */
    fun selectAll() {
        val currentList = currentList
        var uploadCount = 0
        var uploadPauseCount = 0
        val newList = currentList.map {
            when (it.isUploadStatus) {
                FILE_UPLOADING_STATUS -> {
                    uploadCount += 1
                    it.copy(isSelect = false)
                }

                FILE_UPLOAD_INTERRUPT_STATUS -> {
                    uploadPauseCount += 1
                    it.copy(isSelect = true)
                }

                else -> {
                    it.copy(isSelect = true)
                }
            }

        }
        setData(newList)
        selectUpload = uploadPauseCount
        selectCount.value = itemCount - uploadCount
    }

    /**
     * 获取全部选中的文件
     */
    fun getSelectFiles() = currentList.filter {
        it.isSelect
    }.map {
        it.fileEntity
    }


    /**
     * 获取语音指定文件
     */
    fun getVoiceFile(name: String): FileEntity? = currentList.find {
        it.fileEntity.name.substringBeforeLast(".") == name
    }?.fileEntity

    /**
     * 获取最近上传文件
     */
    fun getRecentUploadFile(): FileEntity? = currentList.maxByOrNull {
        it.fileEntity.lastModifyTime
    }?.fileEntity

    /**
     * 获取选中的item数量
     */
    fun getSelectCount(): Int = currentList.count { it.isSelect }

    fun hasSelectUpload(): Boolean {
        return selectUpload > 0
    }

    /**
     * 获取选择状态下所有可选项目的数量
     */
    fun getSelectModeCount(): Int {
        return currentList.count { it.isUploadStatus != FILE_UPLOADING_STATUS }
    }

    /**
     * 删除选中项
     */
    fun delSelect() {
        val currentList = currentList
        val newList = currentList.filter {
            !it.isSelect
        }
        setData(ArrayList(newList))
        selectCount.value = getSelectCount()
    }

    override fun getItemViewType(position: Int): Int {
        return getData(position).fileEntity.fileType.ordinal
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: FileViewEntity) {
        val fileItemView: FileItemView = holder.getView(R.id.itemFileIcon)
        fileItemView.showMode = itemData.showMode
        fileItemView.isSelectMode = itemData.isSelectMode
        fileItemView.fileEntity = itemData.fileEntity
        fileItemView.isSelected = itemData.isSelect
        fileItemView.isUploadStatus = itemData.isUploadStatus
    }

    /**
     * 判断是否需要展示打开方式的菜单
     */
    private fun needCreateOpenMenu(itemData: FileViewEntity): Boolean {
        val belongTo = itemData.fileEntity.belongTo
        if (belongTo in NOT_CREATE_OPEN_MENU_BELONG_LIST) {
            // 云文件, 本地会议录像, 会议录音不显示打开方式菜单
            return false
        }
        return when (itemData.fileEntity.fileType) {
            FileType.DOC, FileType.PPT, FileType.EXCEL, FileType.DOCUMENT -> true
            else -> false
        }
    }

    override fun areItemsTheSame(oldItem: FileViewEntity, newItem: FileViewEntity): Boolean {
        return oldItem.fileEntity.absPath == newItem.fileEntity.absPath
    }

    override fun areContentsTheSame(oldItem: FileViewEntity, newItem: FileViewEntity): Boolean {
        return oldItem == newItem
    }
}