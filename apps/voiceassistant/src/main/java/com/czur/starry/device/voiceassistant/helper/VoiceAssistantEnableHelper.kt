package com.czur.starry.device.voiceassistant.helper

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.czer.starry.device.meetlib.MeetingHandler.localMeetingRecording
import com.czer.starry.device.meetlib.MeetingHandler.localMeetingRecordingLive
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager.appContext
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isHDMIInConnected
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isHDMIInConnectedLive
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantEnabled
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantEnabledLive
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantSupport
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy.USBModeState
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import com.czur.starry.device.hdmilib.HDMIIFStatus
import com.czur.starry.device.hdmilib.HDMIMonitor
import com.czur.starry.device.otalib.OTAHandler.allMeetingStateLive
import com.czur.starry.device.otalib.OTAHandler.allMeetingStatus
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_BYOM_AUDIO_RUNNING
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_BYOM_CAMERA_RUNNING
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_DEVICE_NAME
import com.czur.starry.device.sharescreen.esharelib.SimpleEShareCallback
import com.czur.starry.device.voiceassistant.R
import com.czur.starry.device.voiceassistant.entity.CommandEvent
import com.czur.starry.device.voiceassistant.entity.CommandEventHandler
import com.eshare.serverlibrary.api.EShareCallback
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 *  author : WangHao
 *  time   :2025/06/13
 */

private const val TAG = "VoiceAssistantEnableHelper"

class VoiceAssistantEnableHelper(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val eventHandler: CommandEventHandler
) {

    data class PeripheralMode(val byom: Boolean, val usb: Boolean)

    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(context)
    }

    // 宜享BYOM的状态
    private val peripheralByomRunningFlow = MutableStateFlow(false)

    // USB外设模式的状态
    private val peripheralUSBRunningFlow = MutableStateFlow(false)

    // 外设模式是否运行中
    val peripheralModeRunningFlow =
        combine(peripheralByomRunningFlow, peripheralUSBRunningFlow) { byom, usb ->
            logTagV(TAG, "peripheralModeRunningFlow byom = $byom, usb = $usb")
            PeripheralMode(byom, usb)
        }


    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy { category, eventID, para1, extend1, extend2 ->
            logTagD(
                TAG,
                "category = $category, eventID = $eventID, para1 = $para1, extend1 = $extend1, extend2 = $extend2"
            )
            if (category == 3) {
                lifecycleOwner.launch {
                    refreshGadgetState()
                }
            }
        }
    }
    private companion object {
        private const val TAG = "VoiceAssistantEnableHelper"
        private const val VOICE_ASSISTANT_PROP_KEY = "persist.sys.voice_assistant.enable"
        private const val NAVI_BAR_ACTION = "com.android.systemui.action.UPDATE_VOICE_ASSISTANT_STATUS"
        private const val NAVI_BAR_PACKAGE = "com.android.systemui"
    }

    fun init() {
        registerNotice()
        setupEShareCallback()
        setupObservers()
        refreshInitialStates()
    }

    private fun registerNotice() {
        NoticeHandler.register(
            MsgType(MsgType.SYNC, MsgType.COMMON_PERIPHERAL_USB_CHANGE),
            listener = noticeListener
        )
    }

    private val noticeListener: (msg: NoticeMsg) -> Unit = {
        logTagD(TAG, "gadgetState改变, 刷新USB外设模式")
        lifecycleOwner.lifecycleScope.launch {
            refreshGadgetState()
        }
    }

    /**
     * 刷新USB外设模式
     */
    suspend fun refreshGadgetState() = withContext(Dispatchers.IO) {
        logTagV(TAG, "refreshGadgetState")
        peripheralUSBRunningFlow.value =
            systemManager.getGadgetMode().also {
                logTagV(TAG, "USB外设模式 = $it")
            } == USBModeState.USB_GADGET_STREAM_ON
    }


    private fun setupEShareCallback() {
        lifecycleOwner.lifecycleScope.launch {
            eShareServerSDK.registerCallback(eShareCallback)
        }
    }
    private val eShareCallback: EShareCallback = object : SimpleEShareCallback() {
        override fun onSettingsChanged(key: String, newValue: Any?) {
            logTagD(TAG, "eShareCallback key = ${key}")
            when (key) {
                E_SHARE_BYOM_CAMERA_RUNNING, E_SHARE_BYOM_AUDIO_RUNNING -> {
                    peripheralByomRunningFlow.value = eShareServerSDK.isBYOMRunning
                    logTagD(TAG, "eShareCallback isByomrunning = ${eShareServerSDK.isBYOMRunning}")
                }

                else -> {}
            }
        }
    }



    private fun setupObservers() {
        lifecycleOwner.lifecycleScope.launch {
            lifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                peripheralModeRunningFlow.collect {
                    { updateVoiceAssistantEnable() }
                }
            }
        }

        localMeetingRecordingLive.observe(lifecycleOwner) {
            updateVoiceAssistantEnable()
        }

        isVoiceAssistantEnabledLive.observe(lifecycleOwner) {
            updateVoiceAssistantEnable()
        }

        isHDMIInConnectedLive.observe(lifecycleOwner) {
            updateVoiceAssistantEnable()
        }

        allMeetingStateLive.observe(lifecycleOwner) {
            updateVoiceAssistantEnable()
        }
    }

    private fun refreshInitialStates() {
        lifecycleOwner.lifecycleScope.launch {
            refreshGadgetState()
        }
    }


    private fun updateVoiceAssistantEnable() {
        logTagV(TAG, "updateVoiceAssistantEnable")
        lifecycleOwner.launch {
            peripheralModeRunningFlow.collect { mode ->
                val shouldDisable =
                    isHDMIInConnected || (mode.byom || mode.usb) || localMeetingRecording || allMeetingStatus
                isVoiceAssistantSupport = !shouldDisable
                if (shouldDisable || !isVoiceAssistantEnabled) {
                    setSystemProp(VOICE_ASSISTANT_PROP_KEY, false)
                    eventHandler.handleCommand(CommandEvent(context.getString(R.string.intent_exit)))
                } else {
                    setSystemProp(VOICE_ASSISTANT_PROP_KEY, true)
                }
                sendBroadCastToNaviBar()
            }
        }

    }

    /**
     * 发送广播到导航栏
     */
    private fun sendBroadCastToNaviBar() {
        val intent = Intent().apply {
            `package` = NAVI_BAR_PACKAGE
            action = NAVI_BAR_ACTION
        }
        globalAppCtx.sendBroadcast(intent)
    }

}