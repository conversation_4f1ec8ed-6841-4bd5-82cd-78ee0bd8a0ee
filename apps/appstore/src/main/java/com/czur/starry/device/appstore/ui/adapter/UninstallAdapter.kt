package com.czur.starry.device.appstore.ui.adapter

import android.text.format.Formatter
import android.view.ViewGroup
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.appstore.R
import com.czur.starry.device.appstore.entity.LocalAppInfo
import com.czur.starry.device.appstore.manager.LocalAppManager
import com.czur.starry.device.baselib.base.BaseVH

/**
 * Created by 陈丰尧 on 2021/10/21
 */
class UninstallAdapter : RecyclerView.Adapter<BaseVH>() {
    private val itemCallback = object : DiffUtil.ItemCallback<LocalAppInfo>() {
        override fun areItemsTheSame(
            oldItem: LocalAppInfo,
            newItem: LocalAppInfo
        ): Boolean = oldItem.pkgName == newItem.pkgName

        override fun areContentsTheSame(
            oldItem: LocalAppInfo,
            newItem: LocalAppInfo
        ): Boolean = oldItem == newItem

    }

    private val differ = AsyncListDiffer(this, itemCallback)

    fun setData(newData: List<LocalAppInfo>) {
        differ.submitList(newData)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_uninstall, parent)
    }

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        val itemData = differ.currentList[position]
        holder.setText(itemData.appName, R.id.itemUninstallNameTv)
        holder.setImgDrawable(itemData.appIcon, R.id.itemUninstallIconIv)
        holder.setText(itemData.versionName, R.id.itemUninstallVersionTv)
        val size = Formatter.formatShortFileSize(holder.context, itemData.totalSize)
        holder.setText(size, R.id.itemUninstallSizeTv)

        holder.visible(itemData.uninstalling, R.id.itemUninstallProgress)
            .visible(!itemData.uninstalling, R.id.itemUninstallBtn)

    }

    override fun getItemCount(): Int {
        return differ.currentList.size
    }

    fun getItemData(pos: Int): LocalAppInfo = differ.currentList[pos]

    /**
     * 根据应用名称查找其在列表中的位置，先精确匹配（不区分大小写），若未找到则进行包含匹配（不区分大小写）。
     *
     * @param name 要查找的应用名称。
     * @return 应用在列表中的位置，如果未找到则返回 -1。
     */
    fun getItemPosition(name: String): Int {
        // 先尝试精确匹配
        val exactMatchPos =
            differ.currentList.indexOfFirst { it.appName.equals(name, ignoreCase = true) }
        if (exactMatchPos != -1) {
            return exactMatchPos
        }
        // 若精确匹配未找到，尝试包含匹配
        if (name.length >= 2) {
            return differ.currentList.indexOfFirst { it.appName.contains(name, ignoreCase = true) }
        }
        return -1
    }
}