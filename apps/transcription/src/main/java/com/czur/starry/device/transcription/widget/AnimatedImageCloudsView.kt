package com.czur.starry.device.transcription.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Handler
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.czur.czurutils.log.logTagD
import java.util.concurrent.atomic.AtomicReference
import kotlin.math.pow

class AnimatedImageCloudsView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val ANIM_INTERVAL: Long = 300L
    }

    private val handler = Handler()
    private val scaleHandler = Handler()
    private var currentImageIndex = 0
    private val loopStart = 0
    private val loopEnd = 300
    private val packageName = context.packageName
    private var scaleYFactorStart: Float = 0.8f
    private var scaleYFactorEnd: Float = 0.1f
    private var isQ2Series: Boolean = false
    var latestAmpValue = AtomicReference("0.1")

    private fun updateImage() {
//        logTagD(TAG, "======START======updateImage")
        val resourceName =
//            if (isQ2Series) {
//            "q2_clouds_%05d".format(currentImageIndex)
//        }else {
            "ai_trans_%04d".format(currentImageIndex)
//        }
        val resourceId = context.resources.getIdentifier(resourceName, "drawable", packageName)
        val drawable: Drawable? = ContextCompat.getDrawable(context, resourceId)
        drawable?.let {
            background = it
        }
        // 更新索引
        if (currentImageIndex < loopEnd) {
            currentImageIndex++
        } else {
            // 循环到末尾，回到循环起始点
            currentImageIndex = loopStart
        }

    }

    private fun startAnimation(interval: Long = 10L) {
        visibility = VISIBLE
        handler.postDelayed({
            updateImage()
            startAnimation(interval) // 递归调用保持动画
        }, interval)
    }

    var noSpeakTimes = 0
    fun startScaleAnimation(interval: Long = 700L) {
        visibility = VISIBLE
        var currentInterval = interval
        scaleHandler.postDelayed({
            var ampFloat =
                if (latestAmpValue.get().isEmpty()) 0.1f else latestAmpValue.get().toFloat()
            if (ampFloat < 0.1f) {
                noSpeakTimes++
            } else if (ampFloat >= 0.1f) {
                noSpeakTimes = 0
                // 正在说话,拿当前值
                currentInterval = ANIM_INTERVAL + 50
                val value = adjustCurve(ampFloat)
                startWaveAnimation(value)
            }
            if (noSpeakTimes > 4) {
                // 设置float为0.1
                startWaveAnimation(0.1f)
            }
            startScaleAnimation(currentInterval)// 递归调用保持动画
        }, currentInterval)
    }

    /**
     * 当 x ≤ 0.1 时，y = x
     * 当 0.1 < x ≤ 1 时，y = 0.1 + 0.9 * ( 1 - (1-(x-0.1)/0.9)^4.00 )
     */
    private fun adjustCurve(value: Float): Float {
        // 这里可以根据需要调整曲线的形状
        return if (value < 0.1f) {
            value
        } else {
            0.1f + 0.9f * (1f - (1f - (value - 0.1f) / 0.9f).pow(4.0f))
        }
    }

    fun initAnimation(isQ2Series: Boolean = false) {
        scaleY = 0.1f
        this.isQ2Series = isQ2Series
        visibility = VISIBLE
        updateImage()
        var scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleY", scaleY, 0.1f)
        scaleXAnimator.duration = 10
        scaleXAnimator.start()
        startAnimation()
        startScaleAnimation()
    }

    private var currentAnimator: Animator? = null

    fun startWaveAnimation(value: Float) {
        currentAnimator?.cancel()
        // 波动动画：从当前 scaleY 值放大到 value，再返回
        val waveAnim = ObjectAnimator.ofFloat(this, "scaleY", scaleY, value)
        waveAnim.duration = ANIM_INTERVAL
        waveAnim.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
            }
        }
        )
        waveAnim.start()
        currentAnimator = waveAnim

    }

    fun setAmpValue(string: String) {
        latestAmpValue.set(string)
    }

    fun stopAnimation() {
        handler.removeCallbacksAndMessages(null)
        scaleHandler.removeCallbacksAndMessages(null)
    }

    // 重写onDetachedFromWindow以在View被销毁时停止动画
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
//        stopAnimation()
    }
}