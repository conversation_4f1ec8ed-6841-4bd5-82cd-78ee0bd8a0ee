package com.czur.starry.device.transcription.service

import android.annotation.SuppressLint
import android.app.ActivityManager
import android.audioai.AudioAiServiceCallback
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import com.czur.czurutils.log.logStackTrace
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.transcription.Config.BIG_TRANS_HEIGHT
import com.czur.starry.device.transcription.Config.BIG_TRANS_HEIGHT_TEXT
import com.czur.starry.device.transcription.Config.BIG_TRANS_TEXT_Y
import com.czur.starry.device.transcription.Config.BIG_TRANS_WIDTH
import com.czur.starry.device.transcription.Config.BIG_TRANS_X
import com.czur.starry.device.transcription.Config.BIG_TRANS_Y
import com.czur.starry.device.transcription.Config.DEFAULT_SHOW_CONTENT
import com.czur.starry.device.transcription.Config.DEFAULT_TRANS_CHILD
import com.czur.starry.device.transcription.Config.ERROR_FAILED_CODE_LIST
import com.czur.starry.device.transcription.Config.ERROR_USING_NO_TIME_LIST
import com.czur.starry.device.transcription.Config.GENERATE_MEETING_MINUTES
import com.czur.starry.device.transcription.Config.PREFERENCE_NAME
import com.czur.starry.device.transcription.Config.SHOW_CONTENT
import com.czur.starry.device.transcription.Config.SHOW_CONTENT_TEXT
import com.czur.starry.device.transcription.Config.SHOW_CONTENT_TRANS
import com.czur.starry.device.transcription.Config.SOURCE_LANG
import com.czur.starry.device.transcription.Config.TARGET_LANG
import com.czur.starry.device.transcription.Config.TRANS_CHILD_CONTENT
import com.czur.starry.device.transcription.Config.TRANS_CHILD_SPEECH
import com.czur.starry.device.transcription.Config.TRANS_CHILD_TALK
import com.czur.starry.device.transcription.R
import com.czur.starry.device.transcription.activity.AITransRenameDialogActivity
import com.czur.starry.device.transcription.manager.AudioAiManager
import com.czur.starry.device.transcription.model.AsrResult
import com.czur.starry.device.transcription.util.ActivityManagerWrapper
import com.czur.starry.device.transcription.util.FixedLineHeightTextView
import com.czur.starry.device.transcription.util.FrontEllipsizeHelper
import com.czur.starry.device.transcription.widget.AnimatedImageCloudsView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone


class SubtitleTransAlertWindowService : AlertWindowService() {
    private val TAG = "SubtitleTransAlertWindowService"

    private val sharedPreferences by lazy {
        getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE)
    }

    private val sourceLanguage by lazy {
        sharedPreferences.getString(SOURCE_LANG, "CN")
    }

    private val targetLanguage by lazy {
        sharedPreferences.getString(TARGET_LANG, "EN")
    }

    private val showContent by lazy {
        sharedPreferences.getString(SHOW_CONTENT, DEFAULT_SHOW_CONTENT)
    }

    private val transChildContent by lazy {
        sharedPreferences.getString(TRANS_CHILD_CONTENT, DEFAULT_TRANS_CHILD)
    }

    private val generateMeetingMinutes by lazy {
        sharedPreferences.getBoolean(GENERATE_MEETING_MINUTES, true)
    }

    private lateinit var screenLockReceiver: BroadcastReceiver

    // 网络状态监听
    private val netStatusUtil: NetStatusUtil by lazy {
        NetStatusUtil(this)
    }

    private val wakeupScreenLock by lazy {
        CZPowerManager.createOneWakeLock("aiTransWakeUpScreen")
    }

    // 是否显示网络错误提示
    private var isShowingNetworkError = false

    // 最后一次内容回调时间
    private var lastContentCallbackTime = System.currentTimeMillis()

    override val layoutId: Int
        get() = R.layout.float_text_layout

    //    override val windowWidth: Int
//        get() = BIG_TRANS_WIDTH
    override val windowWidth: Int
        get() = BIG_TRANS_WIDTH
    override val windowHeight: Int
        get() = BIG_TRANS_HEIGHT
    override val xOffSet: Int
        get() = BIG_TRANS_X
    override val yOffset: Int
        get() = BIG_TRANS_Y

    override val careKeyEvent: Boolean = false
    override val draggable: Boolean
        get() = true
    override val autoAdsorption: Boolean
        get() = false

    private val transcriptionCL: ConstraintLayout by ViewFinder(R.id.transcriptionCL)
    private val scaleWindowIv: ImageView by ViewFinder(R.id.scaleWindowIv)
    private val leftResultTv: FixedLineHeightTextView by ViewFinder(R.id.leftResultTv)
    private val leftTransResultTv: FixedLineHeightTextView by ViewFinder(R.id.leftTransResultTv)
    private val rightResultTv: FixedLineHeightTextView by ViewFinder(R.id.rightResultTv)
    private val rightTransResultTv: FixedLineHeightTextView by ViewFinder(R.id.rightTransResultTv)
    private val leftNetErrorTv: TextView by ViewFinder(R.id.leftNetErrorTv)
    private val middleLine: FrameLayout by ViewFinder(R.id.middleLine)
    private val cloudsView: AnimatedImageCloudsView by ViewFinder(R.id.cloudsView)

    private val asrResultList = mutableListOf<AsrResult>()

    private var lastX = BIG_TRANS_X
    private var lastY = BIG_TRANS_Y

    private var leftHistoryContent = ""
    private var leftHistoryTrans = ""
    private var rightHistoryContent = ""
    private var rightHistoryTrans = ""
    private var currentContent = ""
    private var currentTrans = ""

    // 记录开始翻译的时间
    private var startTranslationTime = System.currentTimeMillis()

    private var textInSmall = ""
    private var transInSmall = ""

    private var clearTextJob: Job? = null
    private var startCheckHdmiJob: Job? = null
    private val leftResultContentTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(leftResultTv, 3)
    }

    private val leftResultTransTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(leftTransResultTv, 2)
    }

    private val rightResultContentTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(rightResultTv, 3)
    }

    private val rightResultTransTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(rightTransResultTv, 2)
    }


    // 内部回调类
    private val mAudioAiServiceCallback: AudioAiServiceCallback = object : AudioAiServiceCallback {


        override fun onAudioAsrResult(result: String, roleres: String) {
            makeDataText(isEndOfASentence = false, content = result, roleres = roleres)
        }

        override fun onAudioTranslateResult(result: String, roleres: String) {
            makeDataText(isEndOfASentence = true, content = result, roleres = roleres)
        }

        override fun onAudioAiError(errorCode: Int, errorMsg: String) {
            logTagI(TAG, "onAudioAiError errorCode:$errorCode, errorMsg:$errorMsg")
            when (errorCode) {
                -500, -501, -502 -> {
                    //   java层返回的,不处理,直接和算法服务层的错误码对接
                }

                in ERROR_USING_NO_TIME_LIST -> {//时长不足
                    launch(Dispatchers.IO) {
                        delay(500)
                        withContext(Dispatchers.Main) {
                            NoticeHandler.sendMessage(
                                MsgType(
                                    MsgType.COMMON,
                                    MsgType.COMMON_TOAST
                                )
                            ) {
                                put(getString(R.string.toast_ai_has_no_member_time))
                            }
                            stopTrans()

                        }
                    }
                    logTagI(TAG, "onAudioAiError 会员时长不足")
                }

                in ERROR_FAILED_CODE_LIST -> {//过程中的其他错误
                    launch(Dispatchers.IO) {
                        delay(500)
                        withContext(Dispatchers.Main) {
                            NoticeHandler.sendMessage(
                                MsgType(
                                    MsgType.COMMON,
                                    MsgType.COMMON_TOAST
                                )
                            ) {
                                put(getString(R.string.toast_ai_error_in_using))
                            }
                            stopTrans()
                        }
                    }
                    logTagI(TAG, "onAudioAiError 过程中的其他错误")
                }

                else -> {

                }
            }
        }

        override fun onAsrMicAmp(value: String?) {
            logTagD("song", "onAsrMicAmp $value")
            cloudsView.setAmpValue(if (value.isNullOrEmpty()) "" else value)
        }
    }

    private val activityManagerWrapper by lazy { ActivityManagerWrapper.getInstance() }

    /**
     * 加载最近任务
     */
    suspend fun loadTasks(): List<ActivityManager.RunningTaskInfo> = withContext(Dispatchers.IO) {
        val listTaskInfo = activityManagerWrapper.getRunningTaskList()
        return@withContext listTaskInfo
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        val fileName = intent?.getStringExtra("fileName")
        logTagI(TAG, "SubtitleTransAlertWindowService--onStartCommand fileName--$fileName")

        onScreenOffListener = {
            stopTrans()
        }

        if (showContent == SHOW_CONTENT_TEXT ||
            (showContent != SHOW_CONTENT_TEXT && transChildContent == TRANS_CHILD_SPEECH)
        ) {
            // 实时字幕 或 现场互译-演讲模式
            lastY = if (showContent == SHOW_CONTENT_TEXT) BIG_TRANS_TEXT_Y else lastY
            middleLine.gone()
            ConstraintSet().apply {
                clone(transcriptionCL)
                clear(middleLine.id, ConstraintSet.START) // 取消 start 约束
                applyTo(transcriptionCL)
            }
        } else {
            // 其他情况（如对话模式）
        }
        // 登录状态监听
        UserHandler.isLoginLive.observe(this) {
            // 停止翻译,并刷新UI
            if (!it) {
                launch(Dispatchers.Main) {
                    toast(R.string.toast_ai_rename_success)
                }
                stopTrans(false)
            }
        }

        if (AudioAiManager.getServiceManager() == null) {
            AudioAiManager.init()
        }

        // 使用定义好的观察者变量
        TransHandler.stopTransLive.observe(this@SubtitleTransAlertWindowService) {
            if (it) {
                TransHandler.stopTrans = false
                stopTrans()
            }
        }

        TransHandler.showSubtitlesLive.observe(this@SubtitleTransAlertWindowService) {
            logTagI(TAG, "TransHandler.showSubtitlesLive observe $it")
            changeVis(it)
        }

        logTagI(TAG, "SubtitleTransAlertWindowService--getSystemService")
        AudioAiManager.registerCallback(mAudioAiServiceCallback);
        logTagI(TAG, "mAudioAiServiceManager--${AudioAiManager.getServiceManager()}")
        AudioAiManager.getAsrLangs()?.forEach {
            logTagI(TAG, "getAsrLangs--$it")
        }
        AudioAiManager.getTranslateOrigLangs()?.forEach {
            logTagI(TAG, "translateOrigLangs--$it")
        }
        AudioAiManager.getTranslateTargetLangs()?.forEach {
            logTagI(TAG, "translateTargetLangs--$it")
        }


        AudioAiManager.setTranslateLang(
            sourceLanguage!!, targetLanguage!!
        )
        //比如需要识别多个，那asr语言就传CN==EN==JP==XX \
        // 翻译的话是两个语言${sourceLanguage}==${targetLanguage}
        AudioAiManager.setAsrLang("${sourceLanguage}==${targetLanguage}")
        logTagI(
            TAG, "showContent--${showContent}  " +
                    "generateMeetingMinutes--$generateMeetingMinutes   " +
                    "sourceLanguage--$sourceLanguage   " +
                    "targetLanguage--$targetLanguage"
        )

        when (showContent) {
            SHOW_CONTENT_TEXT -> {// 实时字幕
                AudioAiManager.setTranslateEnabled(false)
            }

            SHOW_CONTENT_TRANS -> {//现场互译
                AudioAiManager.setTranslateEnabled(true)
            }
        }

        val offsetMillis = TimeZone.getDefault().rawOffset
        val offsetHours = offsetMillis / (1000 * 60 * 60)
        val time = (if (offsetHours >= 0) "+" else "") + offsetHours
        logTagI("时区偏移（小时）: UTC$time")

        AudioAiManager.setSummaryEnabled(generateMeetingMinutes)
        AudioAiManager.setAsrName(fileName ?: "")
        AudioAiManager.setTimezone(time)
        AudioAiManager.startAsr()

        // 记录开始翻译的时间
        startTranslationTime = System.currentTimeMillis()
        lastContentCallbackTime = System.currentTimeMillis() // 初始化最后内容回调时间
        logTagI(TAG, "开始翻译，记录时间: $startTranslationTime")

        TransHandler.showSubtitles = true

        return START_NOT_STICKY // 不需要被kill后重建Service
    }

    override fun View.initViews() {

        cloudsView.initAnimation(true)
        // 设置固定行高，避免中英文切换时行高不一致导致的跳动问题
        (leftResultTv as com.czur.starry.device.transcription.util.FixedLineHeightTextView).apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 每行36px的高度
            includeFontPadding = false // 禁用字体内边距
        }

        (leftTransResultTv as com.czur.starry.device.transcription.util.FixedLineHeightTextView).apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 与resultTv使用相同的行高
            includeFontPadding = false
        }

        (rightResultTv as com.czur.starry.device.transcription.util.FixedLineHeightTextView).apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 每行36px的高度
            includeFontPadding = false // 禁用字体内边距
        }

        (rightTransResultTv as com.czur.starry.device.transcription.util.FixedLineHeightTextView).apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 与resultTv使用相同的行高
            includeFontPadding = false
        }

        scaleWindowIv.setOnClickListener { // 缩小到小窗
            TransHandler.showSubtitles = false
        }
    }

    override suspend fun initTask() {
        super.initTask()

    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    override fun initData() {
        super.initData()
        // 初始化 ScreenLockReceiver
        screenLockReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_SCREEN_OFF -> {
                        TransHandler.stopTrans = true
                    }
                }
            }
        }
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_SCREEN_ON)
        }
        registerReceiver(screenLockReceiver, filter)

        // 启动网络状态监听
        netStatusUtil.startWatching()

        // 监听网络状态变化
        netStatusUtil.internetStatusLive.observe(this) { status ->
            logTagI(TAG, "网络状态变化: $status")
            if (status == InternetStatus.DISCONNECT) {
                // 网络断开，显示错误提示
                if (!isShowingNetworkError) {
                    makeDataText(
                        isEndOfASentence = false,
                        content = "",
                        clearText = false,
                        isNetworkError = true
                    )
                }
            } else if (status == InternetStatus.CONNECT && isShowingNetworkError) {
                // 网络恢复，清除错误提示
                isShowingNetworkError = false
                makeDataText(isEndOfASentence = false, content = "", clearText = true)
            }
        }
        // 启动hdmi是否存在的检查
        startCheckHdmiJob()

        // 启动无内容回调自动停止任务
        startNoContentCallbackWatchJob()

        wakeupScreenLock.acquire()

    }

    private fun startCheckHdmiJob() {
        startCheckHdmiJob = launch(Dispatchers.IO) {
            while (true) {
                val loadTasks = loadTasks()
                val findResult =
                    loadTasks.find { it.baseIntent.component?.packageName == "com.ecloud.eairplay" }
                if (findResult != null) {
                    stopTrans()
                }
                delay(3000) // 每2秒检查一次
            }
        }
    }

    private fun startNoContentCallbackWatchJob() {
        clearTextJob = launch(Dispatchers.IO) {
            while (true) {
                // 检查是否超过5分钟没有内容回调
                if ((System.currentTimeMillis() - lastContentCallbackTime) > (5 * ONE_MIN)) {
                    logTagI(TAG, "超过5分钟没有内容回调，自动停止")
                    stopTrans()
                    break
                } else if ((System.currentTimeMillis() - lastContentCallbackTime) > (10 * ONE_SECOND)) {
                    // 10秒没有新内容，清空文本并重置行数计数
                    makeDataText(isEndOfASentence = false, content = "", clearText = true)
                }
                delay(2000) // 每2秒检查一次
            }
        }
    }

    override fun onDestroy() {
        cloudsView.stopAnimation()
        wakeupScreenLock.release()
        clearTextJob?.cancel()
        startCheckHdmiJob?.cancel()

        unregisterReceiver(screenLockReceiver)
        TransHandler.isTranslating = false

        // 停止网络状态监听
        netStatusUtil.stopWatching()
        launch {
            changeNetStatusUI(true)
        }
        super.onDestroy()
    }

    fun makeDataText(
        isEndOfASentence: Boolean,
        content: String,
        clearText: Boolean = false,
        isNetworkError: Boolean = false,
        roleres: String? = sourceLanguage
    ) {
        logTagI(
            TAG,
            " makeDataText-content--$content isEndOfASentence--$isEndOfASentence clearText--$clearText isNetworkError--$isNetworkError" +
                    " roleres--$roleres"
        )
        var textLiveIn: String? = "" // 当前文本应该显示在哪里
        // 如果不是清空文本或网络错误，更新最后内容回调时间
        if (!clearText && !isNetworkError && content.isNotEmpty()) {
            lastContentCallbackTime = System.currentTimeMillis()
        }
        if (showContent == SHOW_CONTENT_TEXT ||
            (showContent != SHOW_CONTENT_TEXT && transChildContent == TRANS_CHILD_SPEECH)
        ) {
            // 实时字幕 或 现场互译-演讲模式, 不关注rollers 都显示在sourceLanguage中
            textLiveIn = sourceLanguage
        } else {
            textLiveIn = roleres
        }
        launch(Dispatchers.IO) {
            currentContent = ""
            currentTrans = ""
            if (isNetworkError) {
                isShowingNetworkError = true
            }

            if (isShowingNetworkError) {
                // 显示网络错误提示
                withContext(Dispatchers.Main) {
                    changeNetStatusUI(false)

                    // 重置行数计数，清除补全记录
                    leftResultContentTvHelper.resetLineCount()
                    leftResultTransTvHelper.resetLineCount()
                    rightResultContentTvHelper.resetLineCount()
                    rightResultTransTvHelper.resetLineCount()

                    leftResultContentTvHelper.setText("", "", false)
                    leftResultTransTvHelper.setText("", "", false)
                    leftHistoryContent = ""
                    leftHistoryTrans = ""
                    rightResultContentTvHelper.setText("", "", false)
                    rightResultTransTvHelper.setText("", "", false)
                    rightHistoryContent = ""
                    rightHistoryTrans = ""
                }
                return@launch
            }

            changeNetStatusUI(true)


            val parts = content.split("=====", limit = 2)
            val currentContent = parts.getOrElse(0) { "" }
            var currentTrans = parts.getOrElse(1) { "" }

            if (showContent == SHOW_CONTENT_TEXT) {
                currentTrans = ""
                leftHistoryTrans = ""
            }

            withContext(Dispatchers.Main) {
                when {
                    TransHandler.showSubtitles == false
                            || clearText -> {
                        // 清空文本时重置行数计数，清除补全记录
                        leftResultContentTvHelper.resetLineCount()
                        leftResultTransTvHelper.resetLineCount()
                        rightResultContentTvHelper.resetLineCount()
                        rightResultTransTvHelper.resetLineCount()
                        // 直接设置空文本，避免使用setText方法可能添加的空白行
                        leftResultTv.text = ""
                        leftTransResultTv.text = ""
                        leftHistoryContent = ""
                        leftHistoryTrans = ""
                        rightResultTv.text = ""
                        rightTransResultTv.text = ""
                        rightHistoryContent = ""
                        rightHistoryTrans = ""
                    }

                    isEndOfASentence -> {

                        when (textLiveIn) {
                            sourceLanguage -> {
                                leftHistoryContent =
                                    leftResultContentTvHelper.setText(
                                        leftHistoryContent,
                                        currentContent,
                                        true
                                    )
                                leftHistoryTrans =
                                    leftResultTransTvHelper.setText(
                                        leftHistoryTrans,
                                        currentTrans,
                                        true
                                    )
                            }

                            targetLanguage -> {
                                rightHistoryContent =
                                    rightResultContentTvHelper.setText(
                                        rightHistoryContent,
                                        currentContent,
                                        true
                                    )
                                rightHistoryTrans =
                                    rightResultTransTvHelper.setText(
                                        rightHistoryTrans,
                                        currentTrans,
                                        true
                                    )
                            }
                        }
                    }

                    else -> {
                        when (textLiveIn) {
                            sourceLanguage -> {
                                leftHistoryContent =
                                    leftResultContentTvHelper.setText(
                                        leftHistoryContent,
                                        currentContent,
                                        false
                                    )
                                leftHistoryTrans =
                                    leftResultTransTvHelper.setText(
                                        leftHistoryTrans,
                                        currentTrans,
                                        false
                                    )
                            }

                            targetLanguage -> {
                                rightHistoryContent =
                                    rightResultContentTvHelper.setText(
                                        rightHistoryContent,
                                        currentContent,
                                        false
                                    )
                                rightHistoryTrans =
                                    rightResultTransTvHelper.setText(
                                        rightHistoryTrans,
                                        currentTrans,
                                        false
                                    )
                            }
                        }
                    }
                }
            }
        }
    }

    private suspend fun changeNetStatusUI(bool: Boolean) {
        withContext(Dispatchers.Main) {
            if (bool){
                leftNetErrorTv.gone()
                if (showContent == SHOW_CONTENT_TRANS && transChildContent == TRANS_CHILD_TALK) {
                    middleLine.show()
                }
            }else{
                leftNetErrorTv.show()
                if (showContent == SHOW_CONTENT_TRANS && transChildContent == TRANS_CHILD_TALK) {
                    middleLine.gone()
                }
            }
        }
    }

    private fun changeVis(vis: Boolean) {
        logTagI(
            TAG,
            "changeVis $vis lastX $lastX lastY $lastY"
        )
        if (vis) {
            if (transcriptionCL.width == BIG_TRANS_WIDTH) {
                logTagI(TAG, "ai互译字幕已显示,return")
                return
            }
            val width = BIG_TRANS_WIDTH
            var height = 0
            if (showContent == SHOW_CONTENT_TEXT) {
                height = BIG_TRANS_HEIGHT_TEXT
            } else {
                height = BIG_TRANS_HEIGHT
            }

            refreshParams(width, height, lastX, lastY)
        } else {
            if (transcriptionCL.width == 1) {
                logTagI(TAG, "ai互译字幕已隐藏,return")
                return
            }
            makeDataText(isEndOfASentence = false, content = "", clearText = true)
            saveCurrentAndRefreshParams()
            refreshParams(1, 1, 1, 1)
        }
    }

    private fun stopTrans(isLogin: Boolean = true) {
        TransHandler.isTranslating = false
        logStackTrace("song")
        val sessionId = AudioAiManager.getSessionId()
        logTagI(TAG, "00getSessionId--$sessionId")
        AudioAiManager.stopAsr(mAudioAiServiceCallback)
        logTagI(TAG, "11getSessionId--$sessionId")

        AudioAiManager.release()
        logTagI(TAG, "22getSessionId--$sessionId")

        // 格式化开始时间为年月日时分秒
        val sdf = SimpleDateFormat("yyyyMMddHHmm", Locale.getDefault())
        val formattedTime = sdf.format(Date(startTranslationTime))
        logTagI(TAG, "停止翻译，使用开始时间作为默认文件名: $formattedTime")

        if (isLogin) {
            val intent = Intent(this, AITransRenameDialogActivity::class.java).apply {
                putExtra("sessionId", sessionId)
                putExtra("defaultFileName", formattedTime)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)
        }

        stopSelf()
    }

    private fun saveCurrentAndRefreshParams() {
        lastX = wmParams!!.x
        lastY = wmParams!!.y
    }
}




