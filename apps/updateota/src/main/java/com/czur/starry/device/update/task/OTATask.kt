package com.czur.starry.device.update.task

import android.content.Intent
import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants.PATH_SDCARD
import com.czur.starry.device.baselib.common.Constants.PATH_SDCARD_OTA
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.allMeetingStatus
import com.czur.starry.device.settings.model.FWVersionModel
import com.czur.starry.device.update.UpdateApp.Companion.app
import com.czur.starry.device.update.UpdateService
import com.czur.starry.device.update.utils.HandleUtils
import com.czur.starry.device.update.utils.RTMUtil
import kotlinx.coroutines.runBlocking
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimerTask
import java.util.concurrent.atomic.AtomicBoolean


class OTATask : TimerTask() {
    companion object {
        const val TAG = "OTATask"
        var isOTARunning = AtomicBoolean(false)
        var isFree = AtomicBoolean(true)
        var fwVersion: FWVersionModel? = null
        var downloadResult = false
    }

    override fun run() {
        logTagD(TAG, "=======OTATask===24")
        //正在运行，视频会议
        if (isOTARunning.get() || allMeetingStatus) {
            logTagD(TAG, "=======isOTARunning.get()===${isOTARunning.get()}")
            logTagD(TAG, "=======allMeetingStatus===${allMeetingStatus}")
            return
        }

        if (RTMUtil.version != null) {
            fwVersion = RTMUtil.version
            RTMUtil.version = null
        } else {
            runBlocking {
                fwVersion = HandleUtils.checkRequest()
            }
            if (fwVersion == null) return
        }


        if (fwVersion!!.update == 0) {
            SPHandler.isreadyForUpdate = false
            OTAHandler.newVersionStatus = false
            return
        } else if (fwVersion!!.update == 1) {
            //可升级
            OTAHandler.newVersionStatus = true
        }

        logTagD(TAG, "=======fwVersion===" + fwVersion.toString())
        logTagD(TAG, SPHandler.firmwareUpdateVersion + "///" + fwVersion!!.version)

        //是否支持当日下载
        if (!SPHandler.isSupportDownload) {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.ROOT)
            val date = dateFormat.format(Date(System.currentTimeMillis()))
            logTagD(TAG, "date===$date")
            logTagD(TAG, "SPHandler.notDownloadDate===" + SPHandler.notDownloadDate)
            if (date == SPHandler.notDownloadDate) return
        }

        isOTARunning.set(true)


        val packageUrl = fwVersion!!.packageUrl
        val md5 = fwVersion!!.md5
        if (SPHandler.firmwareUpdateVersion != fwVersion!!.version) {
            SPHandler.firmwareSize = fwVersion!!.fileSize.toString()
            initParam()
            val lastOTA = PATH_SDCARD_OTA
            val lastFile = File(lastOTA)
            if (lastFile.exists()) {
                lastFile.delete()
            }

            logTagD(TAG, fwVersion.toString())
            val downloadFilePath = PATH_SDCARD + fwVersion!!.version + ".zip"
            val downloadSuccessName =
                PATH_SDCARD_OTA// PATH_SDCARD + fwVersion.version + PATH_OTA_NAME
            logTagD(TAG, downloadFilePath)
            logTagD(TAG, packageUrl)

            runBlocking {
                logTagD(TAG, "OTA文件开始下载")
                downloadResult = HandleUtils.downloadFile(
                    downloadFilePath,
                    packageUrl,
                    fwVersion!!.fileSize
                )
            }
            if (downloadResult) {
                logTagD(TAG, "OTA文件下载成功")
                val checkMD5Result = checkMD5(downloadFilePath, md5)
                if (checkMD5Result) {

                    logTagD(TAG, "OTA文件MD5相同")
//                    FileUtils.deleteOtaFiles(File(PATH_SDCARD))
                    File(downloadFilePath).renameTo(File(downloadSuccessName))
                    HandleUtils.syncDownloadFile()

                    OTAHandler.forceVersionStatus = (fwVersion!!.force == 1)
                    SPHandler.firmwareUpdateFilePath = downloadSuccessName

                    SPHandler.isreadyForUpdate = true
                    SPHandler.isDownLoading = false
                    SPHandler.firmwareUpdateVersion = fwVersion!!.version

                    RTMUtil.version = null

                    if (OTAHandler.forceVersionStatus || SPHandler.updateMaunal!!) {
                        val bootIntent = Intent(app, UpdateService::class.java)
                        bootIntent.apply {
                            putExtra("command", UpdateService.COMMAND_CHECK_LOCAL_UPDATING)
                        }
                        app.startService(bootIntent)
                        SPHandler.updateMaunal = false
                    }
                } else {
                    logTagD(TAG, "OTA文件MD5不相同，删除文件")
                    File(downloadFilePath).delete()
                }
            } else {
                isOTARunning.set(false)
                SPHandler.updateMaunal = false
                logTagD(TAG, "OTA文件下载失败")
            }
        } else {
            logTagD(TAG, "不需要升级")
        }
        logTagD(TAG, "升级流程结束")
        isOTARunning.set(false)
    }

    private fun checkMD5(filePath: String, checkMD5: String): Boolean {
        val checkFile = File(filePath)
        if (!checkFile.exists()) {
            return false
        }
        val md5 = runBlocking { checkFile.md5() }
        return md5 == checkMD5
    }


    private fun initParam() {
        OTAHandler.newVersionStatus = true
        SPHandler.firmwareNeedUpdate = 1
        SPHandler.firmwareUpdateFilePath = null
        SPHandler.isreadyForUpdate = false
//        SPHandler.UpdateMaunal = false
    }

}