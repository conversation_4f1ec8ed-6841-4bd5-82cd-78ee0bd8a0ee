package com.czur.starry.device.update.utils

import android.os.Environment
import android.os.StatFs
import android.text.TextUtils
import androidx.annotation.NonNull
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.settings.model.FWVersionModel
import com.czur.starry.device.update.`interface`.InnerServices
import com.czur.starry.device.update.task.OTATask
import com.czur.starry.device.update.task.OTATask.Companion.TAG
import com.czur.starry.device.update.task.OTATask.Companion.isFree
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.FileOutputStream
import java.io.FilenameFilter
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.util.Timer
import java.util.concurrent.TimeUnit

object HandleUtils {
    var otaTimerTask: OTATask? = null
    var timerTask: Timer? = null
    var version: FWVersionModel? = null
    private var downloadSize = -1

    suspend fun checkRequest() = withContext(Dispatchers.IO) {

        logTagD(OTATask.TAG, "开始OTA检查")
        try {

            val version = Constants.FIRMWARE_NAME
            val sn = Constants.SERIAL

            val httpEntity = HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL)
                .checkFWVersion(sn, version, FWVersionModel::class.java)
            if (httpEntity != null && httpEntity.code == 1000) {
                logTagD(OTATask.TAG, "httpEntity.body==" + httpEntity.body.toString())
                val versionEntity = httpEntity.body
                OTAHandler.newVersionStatus = versionEntity.update != 0
                versionEntity
            } else {
                logTagD(OTATask.TAG, "OTA检查请求失败")
                OTATask.isOTARunning.set(false)
                null
            }
        } catch (e: Exception) {
            logTagD(OTATask.TAG, "OTA检查网络异常")
            e.printStackTrace()
            OTATask.isOTARunning.set(false)
            null
        }
    }


    fun getDownLoadSize(): Int {
        logTagD(OTATask.TAG, "====size=$downloadSize")
        if (!isFree.get()) {
            return -1
        }
        return downloadSize
    }


    private fun getContentLength(mClient: OkHttpClient, url: String): Long {
        val request = Request.Builder().addHeader("Connection", "close").url(url).build()
        var contentLength = 0L
        try {
            mClient.newCall(request).execute().use { response ->
                response.header("Connection", "close")
                if (response.isSuccessful) {
                    contentLength = response.body?.contentLength() ?: 0
                }
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return contentLength
    }

    suspend fun downloadFile(localPath: String, url: String, total: Long = 0L) =
        withContext(Dispatchers.IO) {
            if (getAvailableExternalMemorySize() - total < 1024 * 1024 * 10)
                return@withContext false

            downloadSize = -1
            isFree.set(true)

            val file = File(localPath)
            var temp: File? = null
            try {
                val httpClient = OkHttpClient().newBuilder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build()

                val subEnd = localPath.lastIndexOf(".")
                val zipName = localPath.substring(0, subEnd)

                temp = File("$zipName.temp")
                logTagD(TAG, "=temp===$temp")
                var tempLenth = 0L
                if (temp.exists()) {
                    tempLenth = temp.length()
                } else {
                    enumAllFileList(Constants.PATH_SDCARD, ".temp")
                    enumAllFileList(Constants.PATH_SDCARD, ".zip")
                }
                val tlength = getContentLength(httpClient, url)
                val request = Request.Builder()
                    .addHeader("Connection", "close")
                    .addHeader("RANGE", "bytes=$tempLenth-$tlength")
                    .url(url)
                    .build()

                httpClient.newCall(request).execute().use { response ->
                    response.header("Connection", "close")
                    val body = response.body ?: return@withContext false
                    body.byteStream().use { input ->
                        FileOutputStream(temp, true).use { output ->
                            var buffer = ByteArray(2048)
                            var count = 0

                            while (isFree.get()) {
                                count = input.read(buffer)
                                if (count != -1) {
                                    tempLenth += count
                                    val progress = (tempLenth.toDouble() / tlength * 100).toString()
                                    downloadSize = progress.substringBefore('.').toIntOrNull() ?: 0
                                    output.write(buffer, 0, count)
                                } else {
                                    break
                                }
                            }
                            logTagD(OTATask.TAG, "count==$count")
                        }
                    }
                }

                syncDownloadFile()
                if (isFree.get()) {
                    temp.renameTo(file)
                    syncDownloadFile()
                    delay(2000) // 下载页面每2s获取一次进度
                    isFree.set(false)
                    true
                } else {
                    false
                }
            } catch (e: Exception) {
                isFree.set(false)
                e.printStackTrace()
                false
            }
        }


    //删除版本不匹配.temp后缀文件
    private fun enumAllFileList(dirPath: String, mRegEx: String) {
        if (!TextUtils.isEmpty(dirPath)) {
            val adDir = File(dirPath)
            if (adDir.exists() && adDir.isDirectory) {
                if (!TextUtils.isEmpty(mRegEx)) {
                    val filter = DeleteFileFilter(false, mRegEx)
                    // 2.匹配是不是须要删除的文件
                    val fileList: Array<File> = adDir.listFiles(filter)
                    if (fileList != null && fileList.isNotEmpty()) {
                        for (file in fileList) {
                            if (file.isFile && file.exists()) {
                                val delete = file.delete()
                                logTagI(
                                    TAG,
                                    "delete last ota.temp" + if (delete) "成功~" else "失败！"
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    internal class DeleteFileFilter(
        private val isPrefix: Boolean, // 前缀或后缀规则
        @param:NonNull private val mRegEx: String
    ) :
        FilenameFilter {
        override fun accept(file: File?, s: String): Boolean {
            return if (isPrefix) s.startsWith(mRegEx) else s.endsWith(mRegEx)
        }
    }

    /**
     * 获取手机外部可用空间大小
     * @return
     */
    fun getAvailableExternalMemorySize(): Long {
        if (externalMemoryAvailable()) {
            val path = Environment.getExternalStorageDirectory()
            val stat = StatFs(path.getPath())
            val blockSize = stat.getBlockSize().toLong()
            val availableBlocks = stat.getAvailableBlocks().toLong()
            return availableBlocks * blockSize
//            return  Formatter.formatFileSize(this, blockSize * availableBlocks)
        } else {
            return ERROR
        }
    }

    val ERROR = -1L

    /**
     * 外部存储是否可用
     * @return
     */
    private fun externalMemoryAvailable(): Boolean {
        return android.os.Environment.getExternalStorageState().equals(
            android.os.Environment.MEDIA_MOUNTED
        );
    }


    /**
     * 启动升级
     */
    @Synchronized
    fun startOTATask(isRTM: Boolean = false) {
        //会议中不下载升级
        if (OTATask.isOTARunning.get()) {
            if (isRTM) {
                //查看版本号是否一致
                runBlocking {
                    version = checkRequest()
                }
                if (version == null || version!!.version == RTMUtil.version!!.version) {
                    return
                } else {
                    isFree.set(false)
                    OTATask.isOTARunning.set(false)
                }
            } else {
                return
            }
        }

        timerTask?.cancel()
        timerTask = null
        otaTimerTask?.cancel()
        otaTimerTask = null



        timerTask = Timer()
        otaTimerTask = OTATask()


        logTagD(OTATask.TAG, "====timerTask!!.schedule()")
        timerTask!!.schedule(otaTimerTask, 5000, 2 * 60 * 60 * 1000)


    }


    fun removeOTAPackage() {
        var fileName = "/data/media/0/update.zip"
        var f_ota = File(fileName)
        if (f_ota.exists()) {
            f_ota.delete()
        }
    }

    //同步下载文件
    fun syncDownloadFile() {
        try {
            val process = Runtime.getRuntime().exec("sync")
            val result = process.waitFor()
            logTagD(TAG, "====sync===$result")
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}