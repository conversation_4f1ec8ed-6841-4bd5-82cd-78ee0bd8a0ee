<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />


    <uses-permission android:name="com.czur.starry.contentProvider.rw.sp" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />

    <application
        android:name=".PersonalApp"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <activity
            android:name=".account.UserAccountActivity"
            android:exported="false" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="com.czur.starry.device.personalcenter.BOOT_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".begin.UserBeginNavActivity"
            android:exported="true" />

        <activity android:name=".account.phone.ChangeBindPhoneAty" />
        <activity android:name=".account.ai.recharge.AITransRechargeActivity"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="com.czur.starry.device.personalcenter.BOOT_RECHARGE"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name=".account.ai.order.AiTransOrderActivity" />
        <activity android:name=".account.deactivate.DeactivateActivity"/>

        <provider
            android:name=".provider.PersonalProvider"
            android:authorities="com.czur.starry.device.personalcenter.provider.PersonalProvider"
            android:exported="true"
            tools:ignore="ExportedContentProvider" />
    </application>

</manifest>