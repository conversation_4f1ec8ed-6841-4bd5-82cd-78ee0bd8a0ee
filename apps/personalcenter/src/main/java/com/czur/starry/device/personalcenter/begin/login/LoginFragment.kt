package com.czur.starry.device.personalcenter.begin.login

import android.os.Bundle
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.network.core.common.ResCode
import com.czur.starry.device.baselib.utils.addLoginAccountLengthFilter
import com.czur.starry.device.baselib.utils.keyboard.focusAndShowKeyboard
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.begin.AbsBeginFragment
import com.czur.starry.device.personalcenter.begin.viewmodel.LoginViewModel
import com.czur.starry.device.personalcenter.databinding.FragmentLoginBinding
import com.czur.starry.device.personalcenter.startup.getNetworkErrorDialog
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by 陈丰尧 on 2023/3/9
 */
private const val TAG = "LoginFragment"

class LoginFragment : AbsBeginFragment<FragmentLoginBinding>() {
    private val model: LoginViewModel by viewModels({ requireActivity() })
    private var loadingDialog: LoadingDialog? = null

    private val isLogin = AtomicBoolean(false)

    override fun FragmentLoginBinding.initBindingViews() {
        pwdEyeView.bindToEditText(loginPwdEt)
        // 返回
        loginBackBtn.setOnClickListener {
            logTagV(TAG, "返回")
            findNavController().navigateUp()
        }
        // 登录
        loginBtn.setOnDebounceClickListener {
            logTagD(TAG, "点击登录")
            login()
        }
        // 忘记密码
        loginForgetBtn.setOnClickListener {
            LoginFragmentDirections.actionLoginFragmentToLoginForgetCaptchaFragment().nav()
        }

        // 账号
        loginAccountEt.doAfterTextChanged { text ->
            model.account.value = text?.toString() ?: ""
        }
        loginAccountEt.showSoftInputOnFocus = true
        loginAccountEt.requestFocus()
        loginAccountEt.keyboardShow(300L)
        loginAccountEt.addLoginAccountLengthFilter()
        loginAccountEt.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                (v as EditText).focusAndShowKeyboard(300L)
            }
        }


        // 密码
        loginPwdEt.doAfterTextChanged {
            model.pwd.value = it?.toString() ?: ""
        }
        loginPwdEt.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                (v as EditText).focusAndShowKeyboard(300L)
            }
        }
        loginPwdEt.setOnEditorActionListener { _, actionId, event: KeyEvent? ->
            if (actionId == EditorInfo.IME_ACTION_DONE || event?.keyCode == KeyEvent.KEYCODE_ENTER) {
                // 收起软键盘
                loginPwdEt.keyboardHide()
                if (loginBtn.isEnabled) {
                    loginBtn.performClick()
                }
                true
            } else {
                false
            }
        }


    }

    override fun initData(savedInstanceState: Bundle?) {
        // 页面刷新时, 将View上的数据同步到ViewModel中
        model.account.value = binding.loginAccountEt.text.toString()
        model.pwd.value = binding.loginAccountEt.text.toString()

        model.loginBtnEnable.observe(viewLifecycleOwner) {
            binding.loginBtn.isEnabled = it
        }

        model.errorCode.observe(viewLifecycleOwner) {
            when (it) {
                ResCode.RESULT_CODE_NO_INTERNET, ResCode.RESULT_CODE_NO_NET_CONNECT -> {
                    // 网络错误
                    getNetworkErrorDialog(requireActivity()) {
                        login()
                    }.show()
                }

                ResCode.RESULT_CODE_ERR_USER_PWD -> {
                    // 用户名密码错误
                    toast(R.string.login_error_user_pwd)
                }

                ResCode.RESULT_CODE_INVALID_COUNTRY -> {
                    //非注册区域登陆
                    toast(R.string.login_error_user_countryCode)
                }
            }
            model.errorCodeReset()
        }
    }

    private fun login() {
        if (isLogin.get()) {
            logTagV(TAG, "登录中, 不重复登录")
            return
        }
        isLogin.set(true)
        launch {
            try {
                loadingDialog = LoadingDialog().apply {
                    onDismissListener = {
                        loadingDialog = null
                    }
                    show()
                }
                val res = model.login()
                loadingDialog?.dismiss()
                loadingDialog = null
                if (res) {
                    // 登录成功, 流程结束
                    beginAty.moveToNextStep()
                }
            } finally {
                isLogin.set(false)
            }
        }
    }
}