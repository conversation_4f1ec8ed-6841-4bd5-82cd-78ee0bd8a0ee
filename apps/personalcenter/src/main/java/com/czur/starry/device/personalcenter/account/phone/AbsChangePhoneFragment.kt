package com.czur.starry.device.personalcenter.account.phone

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.network.core.exception.MiaoNetException
import com.czur.starry.device.baselib.network.core.exception.MiaoSMSRequestToMuch
import com.czur.starry.device.baselib.network.core.exception.MiaoVerificationCodeException
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.account.phone.ChangePhoneViewModel.ViewType.*
import com.czur.starry.device.personalcenter.databinding.LayoutChangePhoneBinding

/**
 * Created by 陈丰尧 on 2025/4/21
 */
private const val TAG = "AbsChangePhoneFragment"

abstract class AbsChangePhoneFragment<T> :
    CZViewBindingFragment<LayoutChangePhoneBinding>() {
    protected val viewModel: ChangePhoneViewModel by viewModels()
    abstract val viewType: ChangePhoneViewModel.ViewType

    override fun LayoutChangePhoneBinding.initBindingViews() {
        initSelfViews()
        // 输入的手机号
        captchaPhoneEt.doAfterTextChanged {
            viewModel.updateUserInputPhoneNum(it?.toString() ?: "")
        }

        // 输入的验证码
        captchaEt.doAfterTextChanged {
            viewModel.updateUserInputCaptcha(it?.toString() ?: "")
        }
        captchaEt.setOnKeyListener { _, keyCode, event ->
            if (event.action == android.view.KeyEvent.ACTION_DOWN && keyCode == android.view.KeyEvent.KEYCODE_ENTER) {
                // 按下回车键时，隐藏键盘
                captchaEt.keyboardHide()
                if (captchaNextBtn.isEnabled) {
                    captchaNextBtn.performClick()
                }
                true
            } else {
                false
            }
        }


        getCaptchaTv.setOnDebounceClickListener {
            if (blockGetCaptcha()) {
                return@setOnDebounceClickListener
            }
            // 获取验证码
            launch {
                val result = viewModel.getCaptchaCode(viewType)
                if (result.isSuccess) {
                    viewModel.startGetCaptchaCountDown()
                } else {
                    val tr = result.exceptionOrNull()
                    toastError(tr)
                }
            }
        }

        // 下一步按钮
        captchaNextBtn.setOnDebounceClickListener {
            doOnNextStep()
        }

    }

    protected open fun blockGetCaptcha(): Boolean = false


    protected abstract fun LayoutChangePhoneBinding.initSelfViews()
    protected abstract fun doOnNextStep()

    protected fun toastError(tr: Throwable?) {
        logTagE(TAG, "toastError", tr = tr)
        when (tr) {
            is MiaoSMSRequestToMuch -> toast(R.string.toast_error_sms_request_to_much)
            is MiaoVerificationCodeException -> toast(R.string.toast_error_verification_code)
            is MiaoNetException -> toast(R.string.toast_network_error_retry)
            else -> toastFail()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        // 获取验证码按钮启用状态
        repeatCollectOnResume(viewModel.getBtnEnableFlow) {
            binding.getCaptchaTv.isEnabled = it
        }

        // 观察倒计时状态
        repeatCollectOnResume(viewModel.countDownFlow) { seconds ->
            if (seconds >= 0) {
                binding.countDownTv.text = getString(R.string.str_count_down_sec, seconds)
            }
        }

        // 观察获取验证码按钮可见性
        repeatCollectOnResume(viewModel.captchaButtonVisibilityFlow) { visible ->
            binding.getCaptchaTv.visibility = if (visible) View.VISIBLE else View.GONE
        }

        // 观察倒计时文本可见性
        repeatCollectOnResume(viewModel.countDownVisibilityFlow) { visible ->
            binding.countDownTv.visibility = if (visible) View.VISIBLE else View.GONE
        }

        // 下一步按钮
        repeatCollectOnResume(viewModel.nexBtnEnableFlow) { enable ->
            binding.captchaNextBtn.isEnabled = enable
        }
    }
}