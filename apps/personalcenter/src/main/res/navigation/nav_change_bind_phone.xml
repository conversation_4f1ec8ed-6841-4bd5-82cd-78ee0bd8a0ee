<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_user_begin"
    app:startDestination="@id/changePhoneOldFragment">

    <fragment
        android:id="@+id/changePhoneOldFragment"
        android:name="com.czur.starry.device.personalcenter.account.phone.ChangePhoneOldFragment"
        android:label="ChangePhoneOldFragment"
        tools:layout="@layout/layout_change_phone">
        <action
            android:id="@+id/action_old_to_new"
            app:destination="@id/changePhoneNewFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/changePhoneNewFragment"
        android:name="com.czur.starry.device.personalcenter.account.phone.ChangePhoneNewFragment"
        android:label="ChangePhoneNewFragment"
        tools:layout="@layout/layout_change_phone">
        <action
            android:id="@+id/action_new_to_success"
            app:destination="@id/changePhoneSuccessFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/changePhoneSuccessFragment"
        android:name="com.czur.starry.device.personalcenter.account.phone.ChangePhoneSuccessFragment"
        android:label="ChangePhoneSuccessFragment"
        tools:layout="@layout/layout_change_phone_success" />
</navigation>