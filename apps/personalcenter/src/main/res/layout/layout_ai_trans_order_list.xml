<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/white" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/displayGroup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_main_blue"
        android:visibility="gone"
        tools:ignore="PxUsage">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/orderRv"
            android:layout_width="1400px"
            android:layout_height="720px"
            android:fadeScrollbars="true"
            android:overScrollMode="ifContentScrolls"
            android:scrollbars="vertical"
            app:layout_constraintBottom_toTopOf="@id/claimInvoiceBtn"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:listitem="@layout/item_ai_trans_order" />


        <com.czur.uilib.btn.CZButton
            android:id="@+id/claimInvoiceBtn"
            android:layout_width="300px"
            android:layout_height="80px"
            android:layout_marginTop="30px"
            android:text="@string/str_order_list_claim_invoice"
            android:textSize="30px"
            app:colorStyle="PositiveInBlue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="@id/orderRv"
            app:layout_constraintTop_toBottomOf="@id/orderRv" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/emptyGroup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_main_blue"
        android:visibility="gone">

        <ImageView
            android:id="@+id/emptyIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/img_order_empty"
            app:layout_constraintBottom_toTopOf="@id/emptyTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />


        <TextView
            android:id="@+id/emptyTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="35px"
            android:text="@string/str_order_empty"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/emptyIv" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/noNetworkGroup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_main_blue"
        android:visibility="gone"
        tools:ignore="PxUsage">

        <ImageView
            android:id="@+id/errorIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/img_no_network1"
            android:tintMode="src_atop"
            app:layout_constraintBottom_toTopOf="@id/errorTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:tint="@color/white" />


        <TextView
            android:id="@+id/errorTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="35px"
            android:text="@string/str_no_network"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/errorIv" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_title="@string/app_name" />
</FrameLayout>