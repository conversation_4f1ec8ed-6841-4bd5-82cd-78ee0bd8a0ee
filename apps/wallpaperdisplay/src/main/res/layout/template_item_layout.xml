<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:descendantFocusability="afterDescendants">


    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/im_template"
        android:layout_width="400px"
        android:layout_height="225px"
        android:scaleType="fitXY"
        android:background="@drawable/bg_common"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round="12px" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="400px"
        android:layout_marginTop="10px"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:gravity="center"
        android:text="@string/tv_self_define"
        android:textSize="24px"
        app:layout_constraintLeft_toLeftOf="@id/im_template"
        app:layout_constraintTop_toBottomOf="@+id/im_template" />

    <TextView
        android:id="@+id/tv_stop"
        android:layout_width="120px"
        android:layout_height="50px"
        android:layout_marginRight="10px"
        android:layout_marginBottom="10px"
        android:background="@drawable/bg_text"
        android:gravity="center"
        android:text="@string/str_stop_play"
        android:textColor="#f34949"
        android:textSize="20px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/im_template"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_play_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5px"
        android:layout_marginLeft="10px"
        android:layout_marginTop="15px"
        android:background="@drawable/bg_time"
        android:gravity="center"
        android:text=""
        android:textColor="@color/white"
        android:textSize="18px"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/progress_bar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="70px"
        android:layout_height="70px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/bg_main"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>