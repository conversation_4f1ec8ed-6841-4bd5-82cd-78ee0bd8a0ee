package com.czur.starry.device.noticecenter.watcher.aircontrol

import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.prop.getSystemProp
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2025/7/1
 * 用于监听AirControl的状态变化
 */
class AirControlWatcher : CoroutineScope by MainScope() {
    companion object {
        private const val TAG = "AirControlWatcher"

        // 监控的时间间隔
        private const val WATCH_INTERVAL = 200L // 1秒

        private const val WATCH_KEY_PERSON_TRACK = "sys.camera.persontrack"         // 单人跟踪模式
        private const val WATCH_KEY_WHITE_BOARD = "sys.camera.whiteboard"           // 白板模式
        private const val WATCH_KEY_MOVE_ZOOM = "sys.camera.movezoom"               // 放大缩小移动模式
        private const val WATCH_KEY_MOVE_ZOOM_BORDER = "sys.camera.movezoomborder"  // 放大缩小移动到边界

        const val WATCH_VALUE_IDLE = 0            // 空事件

        const val WATCH_VALUE_PERSON_TRACK_ENABLE = 1          // 单人跟踪模式关闭
        const val WATCH_VALUE_PERSON_TRACK_DISABLE = 2         // 单人跟踪模式开启

        const val WATCH_VALUE_WHITE_BOARD_ENABLE = 1           // 白板模式开启
        const val WATCH_VALUE_WHITE_BOARD_DISABLE = 2           // 白板模式关闭

        const val WATCH_VALUE_MOVE_ZOOM_MOVE = 1          // 放大缩小移动模式-移动
        const val WATCH_VALUE_MOVE_ZOOM_MOVE_LEFT = 2       // 放大缩小移动模式-向左移动
        const val WATCH_VALUE_MOVE_ZOOM_MOVE_RIGHT = 3      // 放大缩小移动模式-向右移动
        const val WATCH_VALUE_MOVE_ZOOM_IN = 4         // 放大缩小移动模式-放大
        const val WATCH_VALUE_MOVE_ZOOM_OUT = 5        // 放大缩小移动模式-缩小

        const val WATCH_VALUE_MOVE_ZOOM_BORDER_LEFT = 1 // 放大缩小移动到边界-左边界
        const val WATCH_VALUE_MOVE_ZOOM_BORDER_RIGHT = 2 // 放大缩小移动到边界-右边界
        const val WATCH_VALUE_MOVE_ZOOM_BORDER_ZOOM_IN = 3 // 放大缩小移动到边界-放大到边界
        const val WATCH_VALUE_MOVE_ZOOM_BORDER_ZOOM_OUT = 4 // 放大缩小移动到边界-缩小到边界
    }

    private var watchJob: Job? = null

    var personTrackListener: ((Int) -> Unit)? = null
    var whiteBoardListener: ((Int) -> Unit)? = null
    var moveZoomListener: ((Int) -> Unit)? = null
    var moveZoomBorderListener: ((Int) -> Unit)? = null

    fun startWatch() {
        logTagD(TAG, "开始监听AirControl状态变化")
        watchJob?.cancel()
        // 启动一个协程来定时检查AirControl的状态
        watchJob = launch {
            while (isActive) {
                try {
                    parsingData()
                } catch (e: Exception) {
                    logTagD(TAG, "解析AirControl数据失败: ${e.message}")
                }
                // 每隔WATCH_INTERVAL毫秒检查一次
                delay(WATCH_INTERVAL)
            }

        }
    }

    private suspend fun parsingData() = withContext(Dispatchers.Default) {
        // 1. 获取数据
        val airControlEnabled = SettingUtil.CameraAndMicSetting.getAirControlEnable()
        val personTrackValue =
            getSystemProp(WATCH_KEY_PERSON_TRACK, WATCH_VALUE_IDLE)
        val whiteBoardValue = getSystemProp(WATCH_KEY_WHITE_BOARD, WATCH_VALUE_IDLE)
        val moveZoomValue = getSystemProp(WATCH_KEY_MOVE_ZOOM, WATCH_VALUE_IDLE)
        val moveZoomBorderValue =
            getSystemProp(WATCH_KEY_MOVE_ZOOM_BORDER, WATCH_VALUE_IDLE)

        /**
         * 重置数据
         */
        suspend fun resetData() {
            if (personTrackValue != WATCH_VALUE_IDLE) {
                setSystemProp(WATCH_KEY_PERSON_TRACK, WATCH_VALUE_IDLE)
            }
            if (whiteBoardValue != WATCH_VALUE_IDLE) {
                setSystemProp(WATCH_KEY_WHITE_BOARD, WATCH_VALUE_IDLE)
            }
            if (moveZoomValue != WATCH_VALUE_IDLE) {
                setSystemProp(WATCH_KEY_MOVE_ZOOM, WATCH_VALUE_IDLE)
            }
            if (moveZoomBorderValue != WATCH_VALUE_IDLE) {
                setSystemProp(WATCH_KEY_MOVE_ZOOM_BORDER, WATCH_VALUE_IDLE)
            }
        }

        // 2. 解析数据
        // 没有开启AirControl, 则不需要处理
        if (!airControlEnabled) {
            // 如果AirControl未开启, 则不需要处理
            resetData() // 重置数据
            return@withContext
        }
        // 如果开启了AirControl, 则需要处理
        if (personTrackValue != WATCH_VALUE_IDLE) {
            withContext(Dispatchers.Main) {
                personTrackListener?.invoke(personTrackValue)
            }
        }
        if (whiteBoardValue != WATCH_VALUE_IDLE) {
            withContext(Dispatchers.Main) {
                whiteBoardListener?.invoke(whiteBoardValue)
            }
        }
        if (moveZoomValue != WATCH_VALUE_IDLE) {
            withContext(Dispatchers.Main) {
                moveZoomListener?.invoke(moveZoomValue)
            }
        }
        if (moveZoomBorderValue != WATCH_VALUE_IDLE) {
            withContext(Dispatchers.Main) {
                moveZoomBorderListener?.invoke(moveZoomBorderValue)
            }
        }
        // 3. 重置数据
        resetData() // 重置数据

    }

    fun stopWatch() {
        logTagD(TAG, "停止监听AirControl状态变化")
        watchJob?.cancel()
        watchJob = null
    }
}